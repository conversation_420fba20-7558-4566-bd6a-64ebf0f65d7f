import { UseGuards } from '@nestjs/common';
import { Args, Context, InputType, Mutation, Query, Resolver } from '@nestjs/graphql';
import { TurnkeyService } from '@lib/internal/turnkey';
import {
    InitOtpAuthInputDto,
    InitOtpAuthResponseDto,
    InputLoginTelegramV2Dto,
    OtpType,
} from '@lib/internal/turnkey/dto/turnkey-auth.dto';

import { Field, ObjectType } from '@nestjs/graphql';
import { GqlAuthGuard } from 'libs/internal/auth/auth.guard';
import { AuthService } from 'libs/internal/auth/auth.service';
import { LoginDTO, RefreshAccessTokenDTO } from 'libs/internal/auth/dto/auth.dto';
import { LoginArgs } from 'libs/internal/auth/dto/login.arg';
import { TelegramAuthService } from 'libs/internal/auth/telegram-auth.service';
import { WalletAuthService } from 'libs/internal/auth/wallet-auth.service';
import { AuthProvider, TurnkeyVersion } from 'libs/internal/users/entities/user.entity';
import {
    GetEmailSubOrgInputDTO,
    GetGoogleSubOrgInputDTO,
    GetTelegramSubOrgInputDTO,
    GetWalletSubOrgInputDTO,
    InitEmailOtpInputDTO,
    InitEmailOtpResponseDTO,
    InputLoginGoogleDto,
    InputLoginWalletV2Dto,
    LoginEmailOtpDTO,
    LoginGoogleDTO,
    LoginV2DTO,
    LoginWithEmailOtpInputDTO,
    SubOrgResponseDTO,
} from 'libs/internal/auth/dto/auth-v2.dto';
import * as crypto from 'crypto';
import { InjectPinoLogger, PinoLogger } from 'nestjs-pino';
import {
    RateLimit,
    RateLimitGuard,
    RATE_LIMIT_WINDOW_MS,
    EMAIL_OTP_INIT_MAX_REQUESTS,
    EMAIL_OTP_LOGIN_MAX_REQUESTS,
} from 'libs/internal/rate-limiting';

@ObjectType()
export class UserDto {
    @Field()
    id: string;

    @Field(() => String, { nullable: true })
    authProvider: AuthProvider;

    @Field({ nullable: true })
    telegramId?: string;

    @Field({ nullable: true })
    telegramUsername?: string;

    @Field({ nullable: true })
    telegramChatId?: number;

    @Field({ nullable: true })
    walletAddress?: string;

    @Field({ nullable: true })
    name?: string;

    @Field({ nullable: true })
    createdAt: Date = new Date();
}

@InputType()
export class TelegramUserDTO {
    @Field()
    id: string;

    @Field()
    username: string;

    @Field()
    firstName: string;

    @Field()
    lastName: string;

    @Field()
    chatId: number;
}

@InputType()
export class GoogleLoginInput {
    @Field()
    idToken: string;
}

@Resolver()
export class AuthResolver {
    constructor(
        private readonly walletAuthService: WalletAuthService,
        private readonly telegramAuthService: TelegramAuthService,
        private readonly authService: AuthService,
        private readonly turnkeyService: TurnkeyService,
        @InjectPinoLogger(AuthResolver.name)
        private readonly logger: PinoLogger,
    ) {}

    @Query(() => String)
    getNonce(@Args('wallAddress') wallAddress: string): string {
        return this.walletAuthService.generateNonce(wallAddress);
    }

    @Mutation(() => LoginDTO)
    async loginByWallet(@Args() data: LoginArgs): Promise<LoginDTO> {
        return await this.walletAuthService.login(data);
    }

    @Mutation(() => LoginDTO)
    async loginByTelegram(
        @Args('userId') userId: string,
        @Args('code') code: string,
        @Args('referrerCode', { nullable: true }) referrerCode?: string,
        @Args('fingerprint', { nullable: true }) fingerprint?: string,
    ): Promise<LoginDTO> {
        return await this.telegramAuthService.login(userId, code);
    }

    @Mutation(() => RefreshAccessTokenDTO)
    async getAccessToken(@Args('refreshToken') refreshToken: string): Promise<RefreshAccessTokenDTO> {
        return this.walletAuthService.refreshAccessToken(refreshToken);
    }

    @Query(() => Boolean)
    @UseGuards(GqlAuthGuard)
    async verifyTOTP(@Context() context: any, @Args('code') code: string): Promise<boolean> {
        return await this.authService.verifyTOTP(context.req.user.sub, code);
    }

    @Mutation(() => SubOrgResponseDTO)
    async createWalletSubOrg(@Args('input') data: GetWalletSubOrgInputDTO): Promise<SubOrgResponseDTO> {
        return this.turnkeyService.createWalletSubOrg(data);
    }

    @Mutation(() => LoginGoogleDTO)
    async loginWithGoogle(@Args('input') data: GetGoogleSubOrgInputDTO): Promise<LoginGoogleDTO> {
        return this.turnkeyService.loginWithGoogle(data);
    }

    @Mutation(() => LoginV2DTO)
    async loginByWalletV2(@Args('input') data: InputLoginWalletV2Dto): Promise<LoginV2DTO> {
        return this.turnkeyService.loginByWalletV2(data);
    }

    // @Mutation(() => LoginGoogleDTO)
    // async loginWithGoogle(@Args('input') data: InputLoginGoogleDto): Promise<LoginGoogleDTO> {
    //     return await this.turnkeyService.loginWithGoogle(data);
    // }

    @Mutation(() => InitOtpAuthResponseDto)
    @UseGuards(RateLimitGuard)
    @RateLimit({
        windowMs: RATE_LIMIT_WINDOW_MS,
        maxRequests: EMAIL_OTP_INIT_MAX_REQUESTS,
        keyPrefix: 'email_otp_init',
        errorType: 'EMAIL_OTP',
    })
    async initEmailOtp(
        @Args('input') data: InitEmailOtpInputDTO,
        @Context() context: any,
    ): Promise<InitOtpAuthResponseDto> {
        const requestIp =
            context.req.headers['cf-connecting-ip'] ||
            context.req.headers['x-forwarded-for'] ||
            context.req.ip ||
            context.req.connection.remoteAddress;
        this.logger.info({
            requestIp,
            headers: context.req.headers,
            remoteAddress: context.req.connection.remoteAddress,
        });
        const hashedIp = crypto.createHash('sha256').update(requestIp).digest('hex');
        return this.turnkeyService.initOtpAuth(data, hashedIp, requestIp);
    }

    @Mutation(() => LoginEmailOtpDTO)
    @UseGuards(RateLimitGuard)
    @RateLimit({
        windowMs: RATE_LIMIT_WINDOW_MS,
        maxRequests: EMAIL_OTP_LOGIN_MAX_REQUESTS,
        keyPrefix: 'email_otp_login',
        errorType: 'LOGIN',
    })
    async loginWithEmailOtp(
        @Args('input') data: LoginWithEmailOtpInputDTO,
        @Context() context: any,
    ): Promise<LoginEmailOtpDTO> {
        const requestIp =
            context.req.headers['cf-connecting-ip'] ||
            context.req.headers['x-forwarded-for'] ||
            context.req.ip ||
            context.req.connection.remoteAddress;
        return this.turnkeyService.loginWithEmailOtp(data, requestIp);
    }

    @Mutation(() => LoginEmailOtpDTO)
    @UseGuards(RateLimitGuard)
    @RateLimit({
        windowMs: RATE_LIMIT_WINDOW_MS,
        maxRequests: EMAIL_OTP_LOGIN_MAX_REQUESTS,
        keyPrefix: 'email_otp_login',
        errorType: 'LOGIN',
    })
    async loginWithEmailOtpV2(
        @Args('input') data: LoginWithEmailOtpInputDTO,
        @Context() context: any,
    ): Promise<LoginEmailOtpDTO> {
        const requestIp =
            context.req.headers['cf-connecting-ip'] ||
            context.req.ip ||
            context.req.headers['x-forwarded-for'] ||
            context.req.connection.remoteAddress;
        return this.turnkeyService.loginWithEmailOtp(data, requestIp, TurnkeyVersion.V2);
    }

    @Mutation(() => LoginGoogleDTO)
    async loginWithGoogleV2(@Args('input') data: GetGoogleSubOrgInputDTO): Promise<LoginGoogleDTO> {
        return this.turnkeyService.loginWithGoogle(data, TurnkeyVersion.V2);
    }

    @Mutation(() => SubOrgResponseDTO)
    async createWalletSubOrgV2(@Args('input') data: GetWalletSubOrgInputDTO): Promise<SubOrgResponseDTO> {
        return this.turnkeyService.createWalletSubOrg(data, TurnkeyVersion.V2);
    }
}

import { Test, TestingModule } from '@nestjs/testing';
import { RateLimitingService } from './rate-limiting.service';
import { RedisService } from 'libs/redis/src';
import { PinoLogger } from 'nestjs-pino';
import { OTP_VERIFICATION_THROTTLE_WINDOW_MS, OTP_VERIFICATION_THROTTLE_MAX_ATTEMPTS } from './rate-limiting.constants';

describe('RateLimitingService - IP Throttling for OTP Verification', () => {
    let service: RateLimitingService;
    let redisService: jest.Mocked<RedisService>;
    let logger: jest.Mocked<PinoLogger>;
    let mockRedisClient: any;

    beforeEach(async () => {
        // Mock Redis client
        mockRedisClient = {
            pipeline: jest.fn().mockReturnThis(),
            zremrangebyscore: jest.fn().mockReturnThis(),
            zadd: jest.fn().mockReturnThis(),
            expire: jest.fn().mockReturnThis(),
            zcard: jest.fn().mockReturnThis(),
            exec: jest.fn().mockResolvedValue([
                [null, 0],
                [null, 0],
            ]),
            del: jest.fn().mockResolvedValue(1),
            zrem: jest.fn().mockResolvedValue(1),
        };

        const module: TestingModule = await Test.createTestingModule({
            providers: [
                RateLimitingService,
                {
                    provide: RedisService,
                    useValue: {
                        getClient: jest.fn().mockReturnValue(mockRedisClient),
                    },
                },
                {
                    provide: PinoLogger,
                    useValue: {
                        warn: jest.fn(),
                        error: jest.fn(),
                        info: jest.fn(),
                    },
                },
            ],
        }).compile();

        service = module.get<RateLimitingService>(RateLimitingService);
        redisService = module.get(RedisService);
        logger = module.get(PinoLogger);
    });

    describe('IP-based OTP verification throttling', () => {
        const testIp = '*************';
        const config = {
            windowMs: OTP_VERIFICATION_THROTTLE_WINDOW_MS,
            maxRequests: OTP_VERIFICATION_THROTTLE_MAX_ATTEMPTS,
            keyPrefix: 'otp_verification_throttle',
        };

        it('should allow OTP verification when IP has no failed attempts', async () => {
            // Mock Redis to return 0 failed attempts
            mockRedisClient.exec.mockResolvedValue([
                [null, 0],
                [null, 0],
            ]);

            const result = await service.checkOtpVerificationThrottleByIp(testIp, config);

            expect(result.allowed).toBe(true);
            expect(result.totalHits).toBe(0);
            expect(result.remainingRequests).toBe(OTP_VERIFICATION_THROTTLE_MAX_ATTEMPTS);
        });

        it('should block OTP verification when IP has exceeded max failed attempts', async () => {
            // Mock Redis to return max failed attempts
            mockRedisClient.exec.mockResolvedValue([
                [null, 0],
                [null, OTP_VERIFICATION_THROTTLE_MAX_ATTEMPTS],
            ]);

            const result = await service.checkOtpVerificationThrottleByIp(testIp, config);

            expect(result.allowed).toBe(false);
            expect(result.totalHits).toBe(OTP_VERIFICATION_THROTTLE_MAX_ATTEMPTS);
            expect(result.remainingRequests).toBe(0);
            expect(logger.warn).toHaveBeenCalledWith(
                expect.objectContaining({
                    currentCount: OTP_VERIFICATION_THROTTLE_MAX_ATTEMPTS,
                    maxRequests: OTP_VERIFICATION_THROTTLE_MAX_ATTEMPTS,
                }),
                'OTP verification throttled by IP due to too many failed attempts',
            );
        });

        it('should record failed OTP verification attempt by IP', async () => {
            await service.recordFailedOtpVerificationByIp(testIp, config);

            expect(mockRedisClient.pipeline).toHaveBeenCalled();
            expect(mockRedisClient.zremrangebyscore).toHaveBeenCalled();
            expect(mockRedisClient.zadd).toHaveBeenCalled();
            expect(mockRedisClient.expire).toHaveBeenCalled();
            expect(mockRedisClient.exec).toHaveBeenCalled();
            expect(logger.info).toHaveBeenCalledWith(
                expect.objectContaining({
                    keyPrefix: config.keyPrefix,
                }),
                'Failed OTP verification attempt recorded by IP',
            );
        });

        it('should clear OTP verification throttle by IP', async () => {
            await service.clearOtpVerificationThrottleByIp(testIp);

            expect(mockRedisClient.del).toHaveBeenCalledWith(
                expect.stringContaining('otp_verification_throttle_by_ip:'),
            );
            expect(logger.info).toHaveBeenCalledWith(
                expect.objectContaining({
                    hashedIp: expect.any(String),
                }),
                'OTP verification throttle cleared by IP',
            );
        });

        it('should handle Redis errors gracefully in checkOtpVerificationThrottleByIp', async () => {
            mockRedisClient.exec.mockRejectedValue(new Error('Redis connection failed'));

            const result = await service.checkOtpVerificationThrottleByIp(testIp, config);

            expect(result.allowed).toBe(true); // Should allow on error
            expect(result.totalHits).toBe(0);
            expect(logger.error).toHaveBeenCalledWith(
                expect.objectContaining({
                    error: 'Redis connection failed',
                }),
                'Error checking OTP verification throttle by IP',
            );
        });

        it('should handle Redis errors gracefully in recordFailedOtpVerificationByIp', async () => {
            mockRedisClient.exec.mockRejectedValue(new Error('Redis connection failed'));

            await service.recordFailedOtpVerificationByIp(testIp, config);

            expect(logger.error).toHaveBeenCalledWith(
                expect.objectContaining({
                    error: 'Redis connection failed',
                }),
                'Error recording failed OTP verification attempt by IP',
            );
        });
    });

    describe('IP hashing for privacy', () => {
        it('should hash IP addresses consistently', () => {
            const testIp = '*************';

            // Call private method through service methods that use it
            const result1 = service['hashIpAddress'](testIp);
            const result2 = service['hashIpAddress'](testIp);

            expect(result1).toBe(result2);
            expect(result1).toHaveLength(64); // SHA256 hex string length
            expect(result1).not.toBe(testIp); // Should be hashed, not plain text
        });

        it('should produce different hashes for different IPs', () => {
            const ip1 = '*************';
            const ip2 = '*************';

            const hash1 = service['hashIpAddress'](ip1);
            const hash2 = service['hashIpAddress'](ip2);

            expect(hash1).not.toBe(hash2);
        });
    });
});

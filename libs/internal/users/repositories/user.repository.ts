import { EntityManager, EntityRepository } from '@mikro-orm/core';
import { AuthProvider, TurnkeyVersion, User } from '../entities/user.entity';
import { TelegramUserDTO } from '../dto/telegram-user.dto';
import { RpcException } from '@nestjs/microservices';
import { GRPC_EXCEPTION } from 'libs/common/api-errors/errors';
import { generateReferrerCode } from './utils';

export class UserRepository extends EntityRepository<User> {
    async getOrCreateUser(
        em: EntityManager,
        walletAddress: string,
        authProvider: AuthProvider,
        version: TurnkeyVersion = TurnkeyVersion.V1,
    ): Promise<{ user: User; isNewUser: boolean }> {
        let user = await em.findOne(User, { walletAddress: walletAddress, authProvider: authProvider });
        let isNewUser = false;

        if (!user) {
            user = new User();
            user.walletAddress = walletAddress;
            user.authProvider = authProvider;
            user.isFirstLogin = true; // Set to true for new users
            while (true) {
                const referrerCode = generateReferrerCode();
                if (!(await this.isReferrerCodeExist(em, referrerCode))) {
                    user.referrerCode = referrerCode;
                    break;
                }
            }
            em.persistAndFlush(user);
            isNewUser = true;
        } else {
            // For existing users logging in, set isFirstLogin to false
            if (user.isFirstLogin) {
                user.isFirstLogin = false;
                em.persistAndFlush(user);
            }
        }

        return { user, isNewUser };
    }

    async getOrCreateUserByTelegramAccount(
        em: EntityManager,
        userDto: TelegramUserDTO,
    ): Promise<{ user: User; isNewUser: boolean }> {
        // Must fork EntityManager because telegram webhook don't auto create RequestContext
        // const em = this.em.fork();
        let user = await em.findOne(User, { telegramId: userDto.id.toString() });
        let isNewUser = false;

        if (!user) {
            user = new User();
            user.authProvider = AuthProvider.TELEGRAM;
            user.telegramId = userDto.id.toString();
            user.telegramChatId = userDto.chatId;
            user.telegramUsername = userDto.username;
            user.name = `${userDto.firstName ?? ''}${userDto.lastName ? ' ' + userDto.lastName : ''}`;
            user.isFirstLogin = true; // Set to true for new users
            user.language = 'en';
            while (true) {
                const referrerCode = generateReferrerCode();
                if (!(await this.isReferrerCodeExist(em, referrerCode))) {
                    user.referrerCode = referrerCode;
                    break;
                }
            }
            em.persistAndFlush(user);
            isNewUser = true;
        } else {
            // For existing users logging in, set isFirstLogin to false
            if (user.isFirstLogin) {
                user.isFirstLogin = false;
                em.persistAndFlush(user);
            }
        }

        return { user, isNewUser };
    }

    async updateUser(user: User) {
        await this.em.persistAndFlush(user);
    }

    async updateUserLanguage(user_id: string, _language: string) {
        await this.em.nativeUpdate(User, { id: user_id }, { language: _language });
    }

    async findOneUser(em: EntityManager, telegram_id: string, user_id: string): Promise<User | null> {
        const conditions: any[] = [];
        if (telegram_id) conditions.push({ telegramId: telegram_id });

        if (user_id !== 'undefined' && user_id !== undefined) conditions.push({ id: user_id });

        const user = await em.findOne(User, conditions.length === 2 ? { $or: conditions } : conditions[0]);
        if (!user) throw new RpcException(GRPC_EXCEPTION.USER_NOT_FOUND);

        return user;
    }

    async isReferrerCodeExist(em: EntityManager, referrerCode: string): Promise<boolean> {
        const user = await em.findOne(User, { referrerCode: referrerCode });
        return user !== null;
    }
}

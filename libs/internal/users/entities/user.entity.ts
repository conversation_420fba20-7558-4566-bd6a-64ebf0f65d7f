import {
    Collection,
    Entity,
    EntityRepositoryType,
    Enum,
    ManyToOne,
    OneToMany,
    OneToOne,
    PrimaryKey,
    Property,
    Type,
} from '@mikro-orm/core';
import { uuidv7 } from 'uuidv7';
import { Field, ObjectType, registerEnumType } from '@nestjs/graphql';
import { UserRepository } from '../repositories/user.repository';
import { UserManagedWallet } from './user-managed-wallet.entity';
import { ActivityLog } from './activity-log.entity';
import { UserDevice } from './user-device.entity';
import { RealUser } from './real-user.entity';
import { UserNotificationPreference } from 'libs/internal/settings/entities/user-notification-preference.entity';
import { UserWithdrawalWhitelistAddress } from 'libs/internal/settings/entities/user-withdrawal-whitelist-address.entity';
import { UserGoogleAuthenticator } from 'libs/internal/settings/entities/user-google-authenticator.entity';
import { UserEmbeddedWallet } from './user-embedded-wallet.entity';
import { SubOrganizationAuth } from './sub-organization-auth.entity';

export enum AuthProvider {
    TELEGRAM = 'TELEGRAM',
    CHAIN_EVM = 'CHAIN_EVM',
    CHAIN_SOL = 'CHAIN_SOL',
    CHAIN_TRON = 'CHAIN_TRON',
    GOOGLE = 'GOOGLE',
    EMAIL = 'EMAIL',
}

export enum TurnkeyVersion {
    V1 = 'v1',
    V2 = 'v2',
}

registerEnumType(AuthProvider, {
    name: 'AuthProvider',
    description: 'Supported provider for authentication',
});

registerEnumType(TurnkeyVersion, {
    name: 'TurnkeyVersion',
    description: 'User account version',
});

@ObjectType()
@Entity({ repository: () => UserRepository })
export class User {
    constructor() {}

    [EntityRepositoryType]?: UserRepository;

    @Field()
    @PrimaryKey({ type: 'uuid' })
    id: string = uuidv7();

    @Field(() => AuthProvider)
    @Enum(() => AuthProvider)
    authProvider: AuthProvider;

    @Property({ nullable: true, unique: true })
    telegramId?: string;

    @Property({ nullable: true })
    telegramUsername?: string;

    @Property({ type: 'bigint', nullable: true, unique: true })
    telegramChatId?: number;

    @Field({ nullable: true })
    @Property({ nullable: true, unique: true })
    walletAddress?: string;

    @Property({ nullable: true })
    name?: string;

    @Property({ defaultRaw: 'now()' })
    createdAt: Date = new Date();

    @Property({ nullable: true, type: 'uuid', unique: true })
    subOrgId: string | null;

    @Property({ nullable: true, type: 'uuid', unique: true })
    turnkeyRootUserId: string | null;

    @OneToMany(() => UserManagedWallet, (userManagedWallet) => userManagedWallet.user)
    userManagedWallets = new Collection<UserManagedWallet>(this);

    @Property({ unique: true, length: 20 })
    referrerCode: string;

    @Property({ type: String, default: 'en' })
    language: string;

    @OneToMany(() => ActivityLog, (activityLog) => activityLog.user)
    activityLogs = new Collection<ActivityLog>(this);

    @OneToMany(() => UserDevice, (userDevice) => userDevice.user)
    devices = new Collection<UserDevice>(this);

    @ManyToOne(() => RealUser, { nullable: true })
    realUser?: RealUser;

    // settings
    @OneToMany(() => UserNotificationPreference, (userNotificationPreference) => userNotificationPreference.user)
    userNotificationPreferences = new Collection<UserNotificationPreference>(this);

    @OneToMany(
        () => UserWithdrawalWhitelistAddress,
        (userWithdrawalWhitelistAddress) => userWithdrawalWhitelistAddress.user,
    )
    userWithdrawalWhitelistAddresses = new Collection<UserWithdrawalWhitelistAddress>(this);

    @OneToOne(() => UserGoogleAuthenticator, (userGoogleAuthenticator) => userGoogleAuthenticator.user)
    userGoogleAuthenticator?: UserGoogleAuthenticator;

    @OneToMany(() => UserEmbeddedWallet, (userEmbeddedWallet) => userEmbeddedWallet.user)
    userEmbeddedWallets = new Collection<UserEmbeddedWallet>(this);

    @Field({ nullable: true })
    @Property({ type: 'varchar', length: 255, nullable: true, unique: true })
    googleId?: string;

    @Field({ nullable: true })
    @Property({ type: 'varchar', length: 255, nullable: true, unique: true })
    email?: string;

    @Field({ nullable: true })
    @Property({ type: 'text', nullable: true })
    avatar?: string;

    @Field(() => Boolean, { defaultValue: false })
    @Property({ type: 'bool', default: false })
    isExportedWallet: boolean;

    @Field(() => Boolean, { defaultValue: true })
    @Property({ type: 'bool', default: true })
    isFirstLogin: boolean;

    @Field(() => TurnkeyVersion)
    @Property({ default: TurnkeyVersion.V1 })
    @Enum(() => TurnkeyVersion)
    turnkeyVersion: TurnkeyVersion;

    @OneToOne(() => SubOrganizationAuth, (subOrganizationAuth) => subOrganizationAuth.user)
    subOrganizationAuth?: SubOrganizationAuth;
}

import { Entity, EntityRepositoryType, Enum, Index, ManyToOne, PrimaryKey, Property, Unique } from '@mikro-orm/core';
import { Field, ObjectType, registerEnumType } from '@nestjs/graphql';
import { User } from './user.entity';
import { uuidv7 } from 'uuidv7';
import { ChainType } from './user-managed-wallet.entity';
import { UserEmbeddedWalletRepository } from '../repositories/user-embedded-wallet.repository';

registerEnumType(ChainType, {
    name: 'ChainType',
    description: 'Supported blockchain types',
});

@ObjectType()
@Entity({ repository: () => UserEmbeddedWalletRepository })
@Unique({ properties: ['walletAddress', 'chain'] })
@Index({ properties: ['walletAddress'] })
@Index({ properties: ['user'] })
export class UserEmbeddedWallet {
    [EntityRepositoryType]?: UserEmbeddedWalletRepository;

    @Field()
    @PrimaryKey({ type: 'uuid' })
    id: string = uuidv7();

    @ManyToOne(() => User)
    user: User;

    @Field(() => ChainType)
    @Enum({ items: () => ChainType, columnType: 'varchar(15)' })
    chain: ChainType;

    @Field()
    @Property({ index: true })
    walletAddress: string;

    @Field()
    @Property({ type: 'uuid' })
    walletId: string;

    @Field()
    @Property({ type: 'uuid' })
    walletAccountId: string;

    @Field()
    @Property({ type: 'varchar', nullable: true })
    hdPath: string;

    @Field()
    @Property({ type: 'varchar', nullable: true })
    name: string;

    @Property({ defaultRaw: 'now()' })
    createdAt: Date = new Date();
}

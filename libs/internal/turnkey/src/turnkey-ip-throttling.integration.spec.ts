import { Test, TestingModule } from '@nestjs/testing';
import { TurnkeyService } from './turnkey.service';
import { RateLimitingService } from 'libs/internal/rate-limiting/src/rate-limiting.service';
import { UsersService } from 'libs/internal/users/users.service';
import { JwtService } from '@nestjs/jwt';
import { PinoLogger } from 'nestjs-pino';
import { RedisService } from 'libs/redis/src';
import {
    OTP_VERIFICATION_THROTTLE_WINDOW_MS,
    OTP_VERIFICATION_THROTTLE_MAX_ATTEMPTS,
} from 'libs/internal/rate-limiting/src/rate-limiting.constants';
import { ApiError } from 'libs/common/src/api-error';
import { OTP_VERIFICATION_THROTTLED } from 'libs/common/src/error-codes';

describe('TurnkeyService - IP Throttling Integration', () => {
    let turnkeyService: TurnkeyService;
    let rateLimitingService: RateLimitingService;
    let mockRedisClient: any;

    const testIp = '*************';
    const testEmail1 = '<EMAIL>';
    const testEmail2 = '<EMAIL>';

    beforeEach(async () => {
        // Mock Redis client
        mockRedisClient = {
            pipeline: jest.fn().mockReturnThis(),
            zremrangebyscore: jest.fn().mockReturnThis(),
            zadd: jest.fn().mockReturnThis(),
            expire: jest.fn().mockReturnThis(),
            zcard: jest.fn().mockReturnThis(),
            exec: jest.fn().mockResolvedValue([
                [null, 0],
                [null, 0],
            ]),
            del: jest.fn().mockResolvedValue(1),
            zrem: jest.fn().mockResolvedValue(1),
        };

        const module: TestingModule = await Test.createTestingModule({
            providers: [
                TurnkeyService,
                RateLimitingService,
                {
                    provide: RedisService,
                    useValue: {
                        getClient: jest.fn().mockReturnValue(mockRedisClient),
                    },
                },
                {
                    provide: PinoLogger,
                    useValue: {
                        warn: jest.fn(),
                        error: jest.fn(),
                        info: jest.fn(),
                        debug: jest.fn(),
                    },
                },
                {
                    provide: UsersService,
                    useValue: {
                        getOrCreateUserByEmail: jest.fn(),
                        getUserByEmail: jest.fn(),
                        getUserById: jest.fn(),
                    },
                },
                {
                    provide: JwtService,
                    useValue: {
                        signAsync: jest.fn().mockResolvedValue('mock-jwt-token'),
                    },
                },
                // Mock other dependencies as needed
            ],
        }).compile();

        turnkeyService = module.get<TurnkeyService>(TurnkeyService);
        rateLimitingService = module.get<RateLimitingService>(RateLimitingService);

        // Mock Turnkey client
        turnkeyService['client'] = {
            initOtp: jest.fn().mockResolvedValue({ otpId: 'mock-otp-id' }),
            verifyOtp: jest.fn(),
        } as any;
    });

    describe('IP Throttling prevents email switching bypass', () => {
        it('should block initEmailOtp when IP is throttled from failed attempts with different email', async () => {
            // Simulate IP being throttled (10 failed attempts)
            mockRedisClient.exec.mockResolvedValue([
                [null, 0], // cleanup result
                [null, OTP_VERIFICATION_THROTTLE_MAX_ATTEMPTS], // current count
            ]);

            const initEmailOtpInput = {
                email: testEmail2, // Different email from the one that caused throttling
                fingerprint: 'test-fingerprint',
            };

            // Should throw throttling error even with different email
            await expect(turnkeyService.initOtpAuth(initEmailOtpInput, 'hashed-ip', testIp)).rejects.toThrow(ApiError);

            // Verify the error is OTP_VERIFICATION_THROTTLED
            try {
                await turnkeyService.initOtpAuth(initEmailOtpInput, 'hashed-ip', testIp);
            } catch (error) {
                expect(error).toBeInstanceOf(ApiError);
                expect(error.code).toBe(OTP_VERIFICATION_THROTTLED.code);
                expect(error.message).toContain('minutes'); // Should contain wait time
            }
        });

        it('should allow initEmailOtp when IP is not throttled', async () => {
            // Simulate IP not being throttled (0 failed attempts)
            mockRedisClient.exec.mockResolvedValue([
                [null, 0], // cleanup result
                [null, 0], // current count
            ]);

            const initEmailOtpInput = {
                email: testEmail1,
                fingerprint: 'test-fingerprint',
            };

            const result = await turnkeyService.initOtpAuth(initEmailOtpInput, 'hashed-ip', testIp);

            expect(result).toBeDefined();
            expect(result.otpId).toBe('mock-otp-id');
        });

        it('should record failed attempts by IP when loginEmailOtp fails', async () => {
            // Mock failed OTP verification
            turnkeyService['client'].verifyOtp = jest.fn().mockResolvedValue({
                verificationToken: null, // Simulate failure
            });

            const loginInput = {
                email: testEmail1,
                otpId: 'test-otp-id',
                otpCode: 'wrong-code',
                targetPublicKey: 'test-public-key',
            };

            // Mock user service to return a user
            const mockUser = {
                id: 'user-id',
                email: testEmail1,
                subOrgId: 'sub-org-id',
            };
            jest.spyOn(turnkeyService['userService'], 'getOrCreateUserByEmail').mockResolvedValue({
                user: mockUser,
                isNewUser: false,
            });

            try {
                await turnkeyService.loginWithEmailOtp(loginInput, 'hashed-ip', testIp);
            } catch (error) {
                // Expected to fail
            }

            // Verify that failed attempt was recorded by IP
            expect(mockRedisClient.pipeline).toHaveBeenCalled();
            expect(mockRedisClient.zadd).toHaveBeenCalled();
        });

        it('should simulate complete scenario: 10 failed attempts with email1, then block initOtp with email2', async () => {
            const config = {
                windowMs: OTP_VERIFICATION_THROTTLE_WINDOW_MS,
                maxRequests: OTP_VERIFICATION_THROTTLE_MAX_ATTEMPTS,
                keyPrefix: 'otp_verification_throttle',
            };

            // Step 1: Record 10 failed attempts by IP (simulating failed loginEmailOtp calls)
            for (let i = 0; i < OTP_VERIFICATION_THROTTLE_MAX_ATTEMPTS; i++) {
                await rateLimitingService.recordFailedOtpVerificationByIp(testIp, config);
            }

            // Step 2: Mock Redis to return max failed attempts for IP check
            mockRedisClient.exec.mockResolvedValue([
                [null, 0], // cleanup result
                [null, OTP_VERIFICATION_THROTTLE_MAX_ATTEMPTS], // current count = max attempts
            ]);

            // Step 3: Try to initEmailOtp with different email - should be blocked
            const initEmailOtpInput = {
                email: testEmail2, // Different email
                fingerprint: 'test-fingerprint',
            };

            await expect(turnkeyService.initOtpAuth(initEmailOtpInput, 'hashed-ip', testIp)).rejects.toThrow(ApiError);

            // Verify the specific error
            try {
                await turnkeyService.initOtpAuth(initEmailOtpInput, 'hashed-ip', testIp);
            } catch (error) {
                expect(error).toBeInstanceOf(ApiError);
                expect(error.code).toBe(OTP_VERIFICATION_THROTTLED.code);
                expect(error.message).toMatch(/\d+ minutes/); // Should contain wait time in minutes
            }
        });

        it('should clear IP throttling after successful login', async () => {
            // Mock successful OTP verification
            turnkeyService['client'].verifyOtp = jest.fn().mockResolvedValue({
                verificationToken: 'valid-token',
            });

            turnkeyService['client'].otpLogin = jest.fn().mockResolvedValue({
                session: 'valid-session',
            });

            const loginInput = {
                email: testEmail1,
                otpId: 'test-otp-id',
                otpCode: 'correct-code',
                targetPublicKey: 'test-public-key',
            };

            // Mock user service
            const mockUser = {
                id: 'user-id',
                email: testEmail1,
                subOrgId: 'sub-org-id',
            };
            jest.spyOn(turnkeyService['userService'], 'getOrCreateUserByEmail').mockResolvedValue({
                user: mockUser,
                isNewUser: false,
            });

            await turnkeyService.loginWithEmailOtp(loginInput, testIp, testIp);

            // Verify that IP throttling was cleared
            expect(mockRedisClient.del).toHaveBeenCalled();
        });
    });
});

import { HttpCode, HttpStatus, Injectable, OnModuleInit } from '@nestjs/common';
import { appConfig } from 'libs/configs';
import {
    ApiKeyStamper,
    DEFAULT_ETHEREUM_ACCOUNTS,
    DEFAULT_SOLANA_ACCOUNTS,
    DEFAULT_TRON_ACCOUNTS,
    Turnkey,
    TurnkeyApiClient,
} from '@turnkey/sdk-server';
import {
    ApiKeyParams,
    ApproveCreateWalletInput,
    ApprovedCreateWalletResponse,
    ApproveDepositHyperLiquidInput,
    ApprovedPassphraseResponse,
    ApprovedPrivateKeyResponse,
    ApproveWithdrawTransactionDto,
    ExportPassphraseInput,
    ExportPrivateKeyInput,
    ExportPrivateKeyWithoutVerifyInput,
    InitOtpAuthResponseDto,
    InputLoginTelegramV2Dto,
    OauthProviderParams,
    OtpType,
    RequestReverifyOtpInputDto,
    ReverifyUserAuthenticationDto,
    TurnKeyLoginResponseDto,
} from './dto/turnkey-auth.dto';
import { UsersService } from 'libs/internal/users/users.service';
import { AuthProvider, TurnkeyVersion, User } from 'libs/internal/users/entities/user.entity';
import { LoginDTO } from 'libs/internal/auth/dto/auth.dto';
import { sign } from 'jsonwebtoken';
import { getDataCheckString } from 'libs/utils/jwt.util';
import * as crypto from 'crypto';
import { JwtService } from '@nestjs/jwt';
import { JwtClaim } from 'libs/internal/auth/auth.vo';
import { AUTH_AGENT } from 'libs/internal/auth/auth.constant';
import { InjectPinoLogger, Logger, PinoLogger } from 'nestjs-pino';
import { ApiError } from 'libs/common/api-errors';
import {
    CHAIN_TYPE_NOT_SUPPORTED,
    EMAIL_RATE_LIMITED,
    EMBEDDED_WALLET_NOT_FOUND,
    EXPORT_PASSPHRASE_ALREADY_CREATED,
    EXPORT_PASSPHRASE_FAILED,
    EXPORT_PRIVATE_KEY_FAILED,
    FAILED_TO_CREATE_WALLET,
    FINGERPRINT_RATE_LIMITED,
    FAILED_TO_WITHDRAW_TRANSACTION,
    INVALID_EMAIL_FORMAT,
    INVALID_GOOGLE_LOGIN,
    INVALID_SIGNATURE_ERROR,
    INVALID_TELEGRAM_LOGIN,
    INVALID_USER_VERIFICATION,
    INVALID_WITHDRAW_TRANSACTION,
    OTP_INIT_FAILED,
    OTP_RATE_LIMITED,
    OTP_VERIFICATION_FAILED,
    OTP_VERIFICATION_THROTTLED,
    SOLANA_WALLET_LIMIT_REACHED,
    TURNKEY_INVALID_ACTIVITY,
    USER_NOT_FOUND,
    WALLET_NAME_ALREADY_EXISTS,
    WALLET_NOT_FOUND,
} from 'libs/common/api-errors/errors';
import { RateLimitingService } from 'libs/internal/rate-limiting/src/rate-limiting.service';
import {
    EMAIL_RATE_LIMIT_WINDOW_MS,
    EMAIL_RATE_LIMIT_MAX_REQUESTS,
    FINGERPRINT_RATE_LIMIT_WINDOW_MS,
    FINGERPRINT_RATE_LIMIT_MAX_REQUESTS,
    OTP_VERIFICATION_THROTTLE_WINDOW_MS,
    OTP_VERIFICATION_THROTTLE_MAX_ATTEMPTS,
    RATE_LIMIT_WINDOW_MS,
    EXPORT_PASSPHRASE_MAX_REQUESTS,
    EXPORT_PRIVATE_KEY_MAX_REQUESTS,
} from 'libs/internal/rate-limiting/src/rate-limiting.constants';
import { LoginArgs } from 'libs/internal/auth/dto/login.arg';
import { WalletAuthService } from 'libs/internal/auth/wallet-auth.service';
import { ChainType } from 'libs/internal/users/entities/user-managed-wallet.entity';
import { EmbeddedWalletDto } from 'libs/internal/users/dto/embedded-wallet.dto';
import {
    GetGoogleSubOrgInputDTO,
    GetTelegramSubOrgInputDTO,
    GetWalletSubOrgInputDTO,
    InitEmailOtpInputDTO,
    InputLoginGoogleDto,
    InputLoginWalletV2Dto,
    LoginEmailOtpDTO,
    LoginGoogleDTO,
    LoginV2DTO,
    LoginWithEmailOtpInputDTO,
    SubOrgResponseDTO,
} from 'libs/internal/auth/dto/auth-v2.dto';
import { getCompressedPublicKey } from '@turnkey/wallet-stamper';
import { LAMPORTS_PER_SOL, PublicKey, SystemProgram, VersionedTransaction } from '@solana/web3.js';
import { GoogleAuthService } from 'libs/internal/auth/google-auth.service';
import {
    TOauthResponse,
    TOtpLoginResponse,
    TStampLoginResponse,
} from '@turnkey/sdk-server/dist/__generated__/sdk_api_types';
import {
    MAIN_ACCOUNT_NAME,
    MAX_SOLANA_WALLET_ACCOUNTS,
    MEME_ADDRESS_WHITELIST,
    RANGO_CONTRACT_WHITELIST,
    ROOT_ORG_NAME,
    TURNKEY_OTP_ALPHANUMERIC,
    TURNKEY_OTP_LENGTH,
    TURNKEY_OTP_TTL,
} from './turnkey.constant';
import { BigNumber } from 'tronweb';
import { getAssociatedTokenAddress } from '@solana/spl-token';
import { TurnkeyApiTypes, TurnkeyClient } from '@turnkey/http';
import { generateP256KeyPair } from './utils';
import { rsaDecrypt } from 'libs/internal/users/repositories/utils';

@Injectable()
export class TurnkeyService implements OnModuleInit {
    private client: TurnkeyApiClient;
    constructor(
        private readonly userService: UsersService,
        private readonly jwtService: JwtService,
        private readonly googleAuthService: GoogleAuthService,
        @InjectPinoLogger(TurnkeyService.name)
        private readonly logger: PinoLogger,
        private readonly walletAuthService: WalletAuthService,
        private readonly rateLimitingService: RateLimitingService,
    ) {}

    async onModuleInit() {
        this.client = new Turnkey({
            apiBaseUrl: 'https://api.turnkey.com',
            apiPrivateKey: appConfig.TURNKEY_API_PRIVATE_KEY,
            apiPublicKey: appConfig.TURNKEY_API_PUBLIC_KEY,
            defaultOrganizationId: appConfig.TURNKEY_ORGANIZATION_ID,
        }).apiClient({
            apiPublicKey: appConfig.TURNKEY_API_PUBLIC_KEY,
            apiPrivateKey: appConfig.TURNKEY_API_PRIVATE_KEY,
        });
    }

    getTurnkeyClient() {
        return this.client;
    }

    /**
     * Helper method to process wallet accounts and assign proper names based on chain type count
     */
    private processWalletAccountsWithNaming(accounts: any[]): Array<EmbeddedWalletDto> {
        const wallets: Array<EmbeddedWalletDto> = [];

        // First pass: collect all wallets by chain type
        const walletsByChain: Record<ChainType, EmbeddedWalletDto[]> = {
            [ChainType.SOLANA]: [],
            [ChainType.EVM]: [],
            [ChainType.ARB]: [],
            [ChainType.TRON]: [],
        };

        accounts.forEach(({ addressFormat, address, walletAccountId, walletId, path }) => {
            switch (addressFormat) {
                case 'ADDRESS_FORMAT_SOLANA':
                    walletsByChain[ChainType.SOLANA].push({
                        chain: ChainType.SOLANA,
                        walletAddress: address,
                        walletAccountId,
                        walletId,
                        hdPath: path,
                        name: MAIN_ACCOUNT_NAME, // Will be updated below
                    });
                    break;
                case 'ADDRESS_FORMAT_ETHEREUM':
                    walletsByChain[ChainType.EVM].push({
                        chain: ChainType.EVM,
                        walletAddress: address,
                        walletAccountId,
                        walletId,
                        hdPath: path,
                        name: MAIN_ACCOUNT_NAME, // Will be updated below
                    });
                    walletsByChain[ChainType.ARB].push({
                        chain: ChainType.ARB,
                        walletAddress: address,
                        walletAccountId,
                        walletId,
                        hdPath: path,
                        name: MAIN_ACCOUNT_NAME, // Will be updated below
                    });
                    break;
                case 'ADDRESS_FORMAT_TRON':
                    walletsByChain[ChainType.TRON].push({
                        chain: ChainType.TRON,
                        walletAddress: address,
                        walletAccountId,
                        walletId,
                        hdPath: path,
                        name: MAIN_ACCOUNT_NAME, // Will be updated below
                    });
                    break;
                default:
                    return;
            }
        });

        // Second pass: assign proper names based on count per chain
        Object.values(ChainType).forEach((chainType) => {
            const chainWallets = walletsByChain[chainType];
            if (chainWallets.length > 1) {
                // Multiple wallets of same chain type - add numbering
                chainWallets.forEach((wallet, index) => {
                    wallet.name = `${MAIN_ACCOUNT_NAME} ${index + 1}`;
                });
            }
            // If only 1 wallet, keep original MAIN_ACCOUNT_NAME
            wallets.push(...chainWallets);
        });

        return wallets;
    }

    async createUserSubOrg(
        userName: string,
        oauth: OauthProviderParams[] = [],
        apiKeys: ApiKeyParams[] = [],
        subOrgName: string,
        email?: string,
        oidcToken?: string,
        version: TurnkeyVersion = TurnkeyVersion.V1,
    ) {
        // Check for existing sub organization based on provided filters
        let existingSubOrg;
        const isNewVersion = version === TurnkeyVersion.V2;

        if (oidcToken) {
            const { organizationIds } = await this.client.getSubOrgIds({
                organizationId: appConfig.TURNKEY_ORGANIZATION_ID,
                filterType: 'OIDC_TOKEN',
                filterValue: oidcToken,
            });

            if (organizationIds?.length) {
                existingSubOrg = { organizationIds };
            }
        }

        if (!existingSubOrg) {
            const { organizationIds } = await this.client.getSubOrgIds({
                organizationId: appConfig.TURNKEY_ORGANIZATION_ID,
                filterType: apiKeys.length ? 'PUBLIC_KEY' : 'USERNAME',
                filterValue: apiKeys.length ? apiKeys[0].publicKey : userName,
            });

            if (organizationIds?.length) {
                existingSubOrg = { organizationIds };
            }
        }

        if (existingSubOrg?.organizationIds?.length) {
            const subOrgId = existingSubOrg.organizationIds[0];
            const { users } = await this.client.getUsers({
                organizationId: subOrgId,
            });

            if (!users || users.length === 0) {
                throw new Error('No users found in the sub organization');
            }

            const user = users.filter((user) => user.userName !== ROOT_ORG_NAME);

            if (!user.length) {
                throw new Error('No user found in the sub organization');
            }

            const userId = user[0].userId;
            const { wallets: allWallets } = await this.client.getWallets({ organizationId: subOrgId });

            const accounts = await this.client.getWalletAccounts({
                organizationId: subOrgId,
                walletId: allWallets?.[0]?.walletId,
            });

            const wallets = this.processWalletAccountsWithNaming(accounts.accounts);

            return { subOrgId, userId, wallets, passphrase: null };
        }

        try {
            let publicKey = appConfig.TURNKEY_API_PUBLIC_KEY;
            let privateKey = appConfig.TURNKEY_API_PRIVATE_KEY;

            if (isNewVersion) {
                const keypair = generateP256KeyPair();
                publicKey = keypair.publicKey;
                privateKey = keypair.privateKey;
            }

            const subOrg = await this.client.createSubOrganization({
                organizationId: appConfig.TURNKEY_ORGANIZATION_ID,
                subOrganizationName: subOrgName,
                rootUsers: [
                    {
                        userName,
                        userEmail: email,
                        oauthProviders: oauth,
                        authenticators: [],
                        apiKeys: apiKeys,
                    },
                    {
                        userName: ROOT_ORG_NAME,
                        apiKeys: [
                            {
                                apiKeyName: 'Root Org API Key',
                                publicKey: publicKey,
                                curveType: 'API_KEY_CURVE_P256',
                            },
                        ],
                        oauthProviders: [],
                        authenticators: [],
                    },
                ],
                rootQuorumThreshold: 1,
                wallet: {
                    walletName: 'Embedded wallets',
                    accounts: [...DEFAULT_SOLANA_ACCOUNTS, ...DEFAULT_ETHEREUM_ACCOUNTS, ...DEFAULT_TRON_ACCOUNTS],
                },
            });

            // Find user-id and root-user-id
            const { users } = await this.client.getUsers({
                organizationId: subOrg.subOrganizationId,
            });

            if (!users || users.length === 0) {
                throw new Error('No users found in the sub organization');
            }

            const user = users.find((user) => user.userName !== ROOT_ORG_NAME);
            const rootUser = users.find((user) => user.userName === ROOT_ORG_NAME);

            if (!user) {
                throw new Error('No user found in the sub organization');
            }

            const userId = user.userId;
            const rootUserId = rootUser ? rootUser.userId : null;

            const memeWhiteList = "['" + Object.values(MEME_ADDRESS_WHITELIST).join("','") + "']";
            const rangoWhiteList = "['" + Object.values(RANGO_CONTRACT_WHITELIST).join("','") + "']";
            const rangoErc20WhiteListData =
                "['" +
                Object.values(RANGO_CONTRACT_WHITELIST)
                    .map((e) => e.slice(2).toLowerCase())
                    .join("','") +
                "']";

            let policies: TurnkeyApiTypes['v1CreatePolicyIntentV3'][] = [
                {
                    policyName: 'SOLANA_ALLOW_SWAP',
                    effect: 'EFFECT_ALLOW',
                    notes: 'Allow swap transactions on Solana',
                    condition: `solana.tx.program_keys.any(p, p in ${memeWhiteList})`,
                },
                {
                    policyName: 'EVM_ALLOW_RANGO',
                    effect: 'EFFECT_ALLOW',
                    notes: 'Allow swap transactions on Rango',
                    condition: `eth.tx.to in ${rangoWhiteList}`,
                },
                {
                    policyName: 'EVM_ALLOW_APPORVE_RANGO',
                    effect: 'EFFECT_ALLOW',
                    notes: 'Allow approve transactions on Rango',
                    condition: `eth.tx.data[0..10] == '0x095ea7b3' && eth.tx.data[34..74] in ${rangoErc20WhiteListData}`,
                },
                {
                    policyName: 'USER_ALLOW_VERIFY_OTP',
                    notes: 'User login otp - approved by system',
                    effect: 'EFFECT_ALLOW',
                    consensus: `approvers.any(user, user.id == '${rootUserId}')`,
                    condition: "activity.type == 'ACTIVITY_TYPE_OTP_LOGIN'",
                },
                {
                    policyName: 'USER_ALLOW_OAUTH_LOGIN',
                    notes: 'User login Google oauth - approved by system',
                    effect: 'EFFECT_ALLOW',
                    consensus: `approvers.any(user, user.id == '${rootUserId}')`,
                    condition: "activity.type == 'ACTIVITY_TYPE_OAUTH_LOGIN'",
                },
                {
                    policyName: 'USER_ADD_OAUTH_PROVIDER',
                    notes: 'Added Google oauth provider - approved by system',
                    effect: 'EFFECT_ALLOW',
                    consensus: `approvers.any(user, user.id == '${rootUserId}')`,
                    condition: "activity.type == 'ACTIVITY_TYPE_CREATE_OAUTH_PROVIDERS'",
                },
                {
                    policyName: 'USER_ALLOW_3RD_WALLET_LOGIN',
                    notes: 'User login 3rd wallet - approved by them self',
                    effect: 'EFFECT_ALLOW',
                    consensus: `approvers.any(user, user.id == '${userId}')`,
                    condition: "activity.type == 'ACTIVITY_TYPE_STAMP_LOGIN'",
                },
            ];

            if (isNewVersion) {
                policies = [
                    {
                        policyName: 'SOLANA_ALLOW_SWAP',
                        effect: 'EFFECT_ALLOW',
                        notes: 'Allow swap transactions on Solana',
                        condition: `solana.tx.program_keys.any(p, p in ${memeWhiteList})`,
                    },
                ];
            }

            let subOrgClient = this.client;
            if (isNewVersion) {
                subOrgClient = new Turnkey({
                    apiBaseUrl: 'https://api.turnkey.com',
                    apiPrivateKey: privateKey,
                    apiPublicKey: publicKey,
                    defaultOrganizationId: subOrg.subOrganizationId,
                }).apiClient();
            }

            const res = await subOrgClient.createPolicies({
                organizationId: subOrg.subOrganizationId,
                policies,
            });
            this.logger.debug('Update root quorum response', { res });

            if (!subOrg.rootUserIds?.length) {
                throw new Error('No root user ID found after creating sub organization');
            }

            const accounts = await subOrgClient.getWalletAccounts({
                organizationId: subOrg.subOrganizationId,
                walletId: subOrg.wallet?.walletId ?? '',
            });

            const wallets = this.processWalletAccountsWithNaming(accounts.accounts);

            const rootQuorumRes = await subOrgClient.updateRootQuorum({
                organizationId: subOrg.subOrganizationId,
                threshold: isNewVersion ? 1 : 2,
                userIds: isNewVersion ? [userId] : (subOrg.rootUserIds ?? []),
            });
            this.logger.debug('Update root quorum response', { res: rootQuorumRes });

            return { subOrgId: subOrg.subOrganizationId, userId, wallets, privateKey, publicKey };
        } catch (error) {
            this.logger.warn('Failed to create sub organization', { err: error });
            throw new Error('Failed to create sub organization');
        }
    }

    // @deprecated
    async loginWithTelegram(input: InputLoginTelegramV2Dto): Promise<LoginDTO> {
        const dataCheckString = getDataCheckString({
            id: input.id,
            first_name: input.firstName,
            username: input.username,
            photo_url: input.photoUrl,
            auth_date: input.authDate,
        });

        const secretKey = crypto.createHash('sha256').update(appConfig.TELEGRAM_BOT_AUTH_TOKEN).digest();

        const computedHash = crypto.createHmac('sha256', secretKey).update(dataCheckString).digest('hex');

        if (computedHash !== input.hash) {
            throw new ApiError(INVALID_TELEGRAM_LOGIN);
        }

        const { user, isNewUser } = await this.userService.getOrCreateUserByTelegramAccount({
            id: input.id.toString(),
            username: input.username,
            firstName: input.firstName,
            lastName: '',
            chatId: undefined,
            fingerprint: undefined, //todo
            referrerCode: undefined, //todo
        });

        if (!user.subOrgId) {
            const userName = `telegram|${input.id}`;
            const userEmail = `telegram.${input.id}@xtechgroup.io`;

            const idToken = sign(
                {
                    iss: 'xbit',
                    sub: input.id.toString(),
                    name: input.firstName,
                    picture: input.photoUrl,
                    aud: 'turnkey.xbit',
                    userName: userName,
                    email: userEmail,
                    exp: Math.floor(Date.now() / 1000) + 3600,
                },
                appConfig.TELEGRAM_OIDC_SECRET,
                { algorithm: 'RS256' },
            );

            const { subOrgId, userId, wallets } = await this.createUserSubOrg(
                userName,
                [
                    // {
                    //     providerName: AuthProvider.TELEGRAM,
                    //     oidcToken: idToken,
                    // },
                ],
                [],
                `telegram|${input.id}`,
                userEmail,
            );

            user.subOrgId = subOrgId;
            user.turnkeyRootUserId = userId;

            await this.userService.updateUser(user);
            await this.userService.createUserEmbeddedWallets(user, wallets);
        }

        const payload: JwtClaim = {
            sub: user.id,
            iss: AUTH_AGENT,
        };

        return {
            accessToken: await this.jwtService.signAsync(payload),
            refreshToken: await this.jwtService.signAsync(payload, {
                secret: appConfig.JWT_REFRESH_SECRET,
                expiresIn: appConfig.REFRESH_TOKEN_EXPIRES_IN,
            }),
            userId: user.id,
        };
    }

    async loginWithGoogle(
        input: GetGoogleSubOrgInputDTO,
        turnkeyVersion: TurnkeyVersion = TurnkeyVersion.V1,
    ): Promise<LoginGoogleDTO> {
        // Verify Google token
        const googleUser = await this.googleAuthService.verifyGoogleToken(input.idToken);

        // Find or create user in the system
        const { user, isNewUser } = await this.userService.getOrCreateUserByEmail({
            email: googleUser.email ?? '',
            fingerprint: input.fingerprint,
            referrerCode: input.referrerCode,
            provider: AuthProvider.GOOGLE,
            turnkeyVersion: turnkeyVersion,
        });

        // If the user does not have a subOrgId, create a subOrg on Turnkey with the Google token
        if (!user.subOrgId) {
            const userName = `email|${googleUser.sub}`;
            const userEmail = googleUser.email;

            try {
                const { subOrgId, userId, wallets, privateKey, publicKey } = await this.createUserSubOrg(
                    userName,
                    [
                        {
                            providerName: AuthProvider.GOOGLE,
                            oidcToken: input.idToken,
                        },
                    ],
                    [], // No API keys
                    `email|${googleUser.sub}`,
                    userEmail,
                    input.idToken,
                    turnkeyVersion,
                );

                user.subOrgId = subOrgId;
                user.turnkeyRootUserId = userId;
                user.googleId = googleUser.sub;
                user.email = googleUser.email;
                user.name = googleUser.name;
                user.avatar = googleUser.picture;

                await this.userService.updateUser(user);
                await this.userService.createUserEmbeddedWallets(user, wallets);

                if (turnkeyVersion === TurnkeyVersion.V2) {
                    await this.userService.createTurnKeySubOrganizationAuth(user, {
                        subOrgId,
                        privateKey: privateKey ?? '',
                        publicKey: publicKey ?? '',
                    });
                }
            } catch (error) {
                this.logger.warn('Failed to create Google sub organization', { err: error });
                throw new Error('Failed to create Google sub organization');
            }
        }

        if (user.authProvider !== AuthProvider.GOOGLE && !user.googleId) {
            // add google auth
            const oauth = await this.client.getOauthProviders({
                organizationId: user.subOrgId ?? '',
                userId: user.turnkeyRootUserId ?? '',
            });

            // todo: cache redis checking
            if (!oauth.oauthProviders.length) {
                try {
                    const turnkeyResponse = await this.client.createOauthProviders({
                        userId: user.turnkeyRootUserId ?? '',
                        organizationId: user.subOrgId ?? '',
                        oauthProviders: [
                            {
                                providerName: AuthProvider.GOOGLE,
                                oidcToken: input.idToken,
                            },
                        ],
                    });

                    if (turnkeyResponse.providerIds.length === 0) {
                        throw new ApiError(INVALID_GOOGLE_LOGIN);
                    }

                    user.googleId = googleUser.sub;
                    user.email = googleUser.email;
                    user.name = googleUser.name;
                    user.avatar = googleUser.picture;
                    await this.userService.updateUser(user);
                } catch (error) {
                    this.logger.error(error);
                    throw new ApiError(INVALID_GOOGLE_LOGIN);
                }
            }
        }

        const startTime = Date.now();
        const turnkeyResponse = await this.client.oauthLogin({
            organizationId: user.subOrgId ?? '',
            oidcToken: input.idToken,
            publicKey: input.targetPublicKey,
            expirationSeconds: '604800', // 7 days
        });

        this.logger.debug({
            response: turnkeyResponse,
            duration: Date.now() - startTime,
        });

        const payload: JwtClaim = {
            sub: user.id,
            iss: AUTH_AGENT,
        };

        return {
            accessToken: await this.jwtService.signAsync(payload),
            refreshToken: await this.jwtService.signAsync(payload, {
                secret: appConfig.JWT_REFRESH_SECRET,
                expiresIn: appConfig.REFRESH_TOKEN_EXPIRES_IN,
            }),
            userId: user.id,
            subOrgId: user.subOrgId ?? '',
            fingerprint: input.fingerprint,
            referrerCode: input.referrerCode,
            turnKeyResponse: {
                session: turnkeyResponse.session,
            },
        };
    }

    async loginByWallet(
        data: LoginArgs,
        version: TurnkeyVersion = TurnkeyVersion.V1,
    ): Promise<LoginDTO | PromiseLike<LoginDTO>> {
        const walletAddress = data.isOkxWallet
            ? this.walletAuthService.extractOkxWalletAddress(data.message)
            : this.walletAuthService.extractWalletAddress(data.message);
        let authProvider: AuthProvider;
        switch (data.chainType) {
            case ChainType.EVM:
                if (!this.walletAuthService.verifyEVMSignature(data.message, data.signature, walletAddress)) {
                    throw new ApiError(INVALID_SIGNATURE_ERROR);
                }
                authProvider = AuthProvider.CHAIN_EVM;
                break;
            case ChainType.SOLANA:
                if (!this.walletAuthService.verifySolSignature(data.message, data.signature, walletAddress)) {
                    throw new ApiError(INVALID_SIGNATURE_ERROR);
                }
                authProvider = AuthProvider.CHAIN_SOL;
                break;
            case ChainType.TRON: {
                authProvider = AuthProvider.CHAIN_TRON;
                const isValid = await this.walletAuthService.verifyTrxSignature(
                    data.message,
                    data.signature,
                    walletAddress,
                );
                if (!isValid) {
                    throw new ApiError(INVALID_SIGNATURE_ERROR);
                }
                break;
            }
            default:
                throw new ApiError(CHAIN_TYPE_NOT_SUPPORTED);
        }
        const user = await this.userService.getOrCreateUserByWallet(
            walletAddress,
            authProvider,
            data.referrerCode,
            data.fingerprint,
        );

        if (!user.subOrgId) {
            const { subOrgId, userId, wallets, privateKey, publicKey } = await this.createUserSubOrg(
                '',
                [],
                [
                    {
                        apiKeyName: 'Wallet Auth - Embedded Wallet',
                        publicKey: walletAddress,
                        curveType:
                            data.chainType === ChainType.EVM
                                ? ('API_KEY_CURVE_SECP256K1' as const)
                                : ('API_KEY_CURVE_ED25519' as const),
                    },
                ],
                `${data.chainType}|${walletAddress}`,
                undefined,
                undefined,
                version,
            );

            user.subOrgId = subOrgId;
            user.turnkeyRootUserId = userId;

            await this.userService.updateUser(user);
            await this.userService.createUserEmbeddedWallets(user, wallets);

            if (version === TurnkeyVersion.V2) {
                await this.userService.createTurnKeySubOrganizationAuth(user, {
                    subOrgId,
                    privateKey: privateKey ?? '',
                    publicKey: publicKey ?? '',
                });
            }
        }

        const payload: JwtClaim = {
            sub: user.id,
            iss: AUTH_AGENT,
        };
        return {
            accessToken: await this.jwtService.signAsync(payload),
            refreshToken: await this.jwtService.signAsync(payload, {
                secret: appConfig.JWT_REFRESH_SECRET,
                expiresIn: appConfig.REFRESH_TOKEN_EXPIRES_IN,
            }),
            userId: user.id,
        } as LoginDTO;
    }

    async createWalletSubOrg(
        data: GetWalletSubOrgInputDTO,
        version: TurnkeyVersion = TurnkeyVersion.V1,
    ): Promise<SubOrgResponseDTO> {
        const walletAddress = data.isOkxWallet
            ? this.walletAuthService.extractOkxWalletAddress(data.message)
            : this.walletAuthService.extractWalletAddress(data.message);

        let authProvider: AuthProvider;
        let publicKey: string = '';
        const message = data.message;

        switch (data.chainType) {
            case ChainType.EVM:
                if (!this.walletAuthService.verifyEVMSignature(message, data.signature, walletAddress)) {
                    throw new ApiError(INVALID_SIGNATURE_ERROR);
                }
                authProvider = AuthProvider.CHAIN_EVM;
                if (!data.signature.startsWith('0x')) {
                    data.signature = '0x' + data.signature;
                }

                publicKey = await getCompressedPublicKey(data.signature as `0x${string}`, message);
                break;
            case ChainType.SOLANA:
                if (!this.walletAuthService.verifySolSignature(message, data.signature, walletAddress)) {
                    throw new ApiError(INVALID_SIGNATURE_ERROR);
                }
                authProvider = AuthProvider.CHAIN_SOL;
                publicKey = Buffer.from(new PublicKey(walletAddress).toBuffer()).toString('hex');
                break;
            case ChainType.TRON: {
                authProvider = AuthProvider.CHAIN_TRON;
                const isValid = await this.walletAuthService.verifyTrxSignature(message, data.signature, walletAddress);
                if (!isValid) {
                    throw new ApiError(INVALID_SIGNATURE_ERROR);
                }
                break;
            }
            default:
                throw new ApiError(CHAIN_TYPE_NOT_SUPPORTED);
        }

        const user = await this.userService.getOrCreateUserByWallet(
            walletAddress,
            authProvider,
            data.referrerCode,
            data.fingerprint,
            undefined,
            version,
        );

        if (!user.subOrgId) {
            const {
                subOrgId,
                userId,
                wallets,
                privateKey,
                publicKey: apiPublicKey,
            } = await this.createUserSubOrg(
                '',
                [],
                [
                    {
                        apiKeyName: 'Wallet Auth - Embedded Wallet',
                        publicKey: publicKey,
                        curveType:
                            data.chainType === ChainType.EVM
                                ? ('API_KEY_CURVE_SECP256K1' as const)
                                : ('API_KEY_CURVE_ED25519' as const),
                    },
                ],
                `${data.chainType}|${walletAddress}`,
                undefined,
                undefined,
                version,
            );

            user.subOrgId = subOrgId;
            user.turnkeyRootUserId = userId;

            await this.userService.updateUser(user);
            await this.userService.createUserEmbeddedWallets(user, wallets);

            if (version === TurnkeyVersion.V2) {
                await this.userService.createTurnKeySubOrganizationAuth(user, {
                    subOrgId,
                    privateKey: privateKey ?? '',
                    publicKey: apiPublicKey ?? '',
                });
            }
        }

        return {
            subOrgId: user.subOrgId ?? '',
            userId: user.id,
        };
    }

    // @deprecated
    async createTelegramSubOrg(input: GetTelegramSubOrgInputDTO): Promise<SubOrgResponseDTO> {
        const dataCheckString = getDataCheckString({
            id: input.id,
            first_name: input.firstName,
            username: input.username,
            photo_url: input.photoUrl,
            auth_date: input.authDate,
        });

        const secretKey = crypto.createHash('sha256').update(appConfig.TELEGRAM_BOT_AUTH_TOKEN).digest();

        const computedHash = crypto.createHmac('sha256', secretKey).update(dataCheckString).digest('hex');

        if (computedHash !== input.hash) {
            throw new ApiError(INVALID_TELEGRAM_LOGIN);
        }

        const { user, isNewUser } = await this.userService.getOrCreateUserByTelegramAccount({
            id: input.id.toString(),
            username: input.username,
            firstName: input.firstName,
            lastName: '',
            chatId: undefined,
            fingerprint: undefined, //todo
            referrerCode: undefined, //todo
        });

        if (!user.subOrgId) {
            const userName = `telegram|${input.id}`;
            const userEmail = `telegram.${input.id}@xtechgroup.io`;

            const idToken = sign(
                {
                    iss: 'xbit',
                    sub: input.id.toString(),
                    name: input.firstName,
                    picture: input.photoUrl,
                    aud: 'turnkey.xbit',
                    userName: userName,
                    email: userEmail,
                    exp: Math.floor(Date.now() / 1000) + 3600,
                },
                appConfig.TELEGRAM_OIDC_SECRET,
                { algorithm: 'RS256' },
            );

            const { subOrgId, userId, wallets } = await this.createUserSubOrg(
                userName,
                [
                    // {
                    //     providerName: AuthProvider.TELEGRAM,
                    //     oidcToken: idToken,
                    // },
                ],
                [],
                `telegram|${input.id}`,
                userEmail,
            );

            user.subOrgId = subOrgId;
            user.turnkeyRootUserId = userId;

            await this.userService.updateUser(user);
            await this.userService.createUserEmbeddedWallets(user, wallets);
        }

        return {
            subOrgId: user.subOrgId ?? '',
            userId: user.id,
        };
    }

    async signUserTransaction(userId: string, chainType: ChainType, transaction: string, address: string) {
        try {
            const wallet = await this.userService.getUserEmbeddedWallet(userId, address, chainType);
            if (!wallet) {
                throw new Error('Wallet not found');
            }

            if (!wallet.user.subOrgId) {
                throw new Error('User sub organization ID not found');
            }

            let client = this.client;

            if (wallet.user.turnkeyVersion === TurnkeyVersion.V2) {
                const privateKey = rsaDecrypt(
                    wallet.user.subOrganizationAuth?.encryptedPrivateKey ?? '',
                    `${wallet.user.subOrgId}-${wallet.user.id}`,
                );

                client = new Turnkey({
                    apiBaseUrl: 'https://api.turnkey.com',
                    apiPrivateKey: privateKey,
                    apiPublicKey: wallet.user.subOrganizationAuth?.publicKey ?? '',
                    defaultOrganizationId: wallet.user.subOrgId,
                }).apiClient();
            }

            const startTime = Date.now();
            const sig = await client.signTransaction({
                type: [ChainType.EVM, ChainType.ARB].includes(chainType)
                    ? 'TRANSACTION_TYPE_ETHEREUM'
                    : 'TRANSACTION_TYPE_SOLANA',
                organizationId: wallet.user.subOrgId,
                signWith: wallet.walletAddress,
                unsignedTransaction: transaction,
            });

            this.logger.info({ msg: 'Turnkey Signing response time ms', consume: Date.now() - startTime });

            return sig.signedTransaction;
        } catch (error) {
            this.logger.error(error);
            throw error;
        }
    }

    // @deprecated
    async signUserRawPayloadTransaction(user: User, chainType: ChainType, signableData: string) {
        const wallets = await this.userService.getUserEmbeddedWalletsWithoutBalance(user);

        const wallet = wallets.find((w) => w.chain === chainType);

        if (!wallet) {
            throw new Error('Wallet not found');
        }

        if (!user.subOrgId) {
            throw new Error('User sub organization ID not found');
        }

        try {
            const sig = await this.client.signRawPayload({
                organizationId: user.subOrgId,
                signWith: wallet.walletAddress,
                payload: signableData,
                encoding: 'PAYLOAD_ENCODING_HEXADECIMAL',
                hashFunction: 'HASH_FUNCTION_KECCAK256',
            });

            return sig;
        } catch (error) {
            if (error.code === 7) {
                const activityIdMatch = error.message.match(/activity \("([^"]+)"\)/);
                if (activityIdMatch) {
                    return {
                        activityId: activityIdMatch[1],
                    };
                }
            }
            this.logger.error('Failed to sign raw payload', { error });
            throw new Error('Failed to sign raw payload');
        }
    }

    async approveUserRawPayloadTransaction(
        activityId: string,
        user: User,
        chainType: ChainType,
        signableData: string,
    ): Promise<{
        signature: {
            r: string;
            s: string;
            v: number;
        };
    }> {
        const wallets = await this.userService.getUserEmbeddedWalletsWithoutBalance(user);

        const wallet = wallets.find((w) => w.chain === chainType);

        if (!wallet) {
            throw new Error('Wallet not found');
        }

        if (!user.subOrgId) {
            throw new Error('User sub organization ID not found');
        }

        const activity = await this.client.getActivity({
            organizationId: user.subOrgId,
            activityId,
        });

        if (!activity) {
            throw new Error('Activity not found');
        }

        if (!activity.activity.intent.signRawPayloadIntentV2) {
            throw new Error('Activity intent is not signRawPayloadIntent');
        }

        if (activity.activity.intent.signRawPayloadIntentV2.hashFunction != 'HASH_FUNCTION_KECCAK256') {
            throw new Error('Activity intent is not compatible with KECCAK256 hash function');
        }

        if (signableData != activity.activity.intent.signRawPayloadIntentV2?.payload) {
            throw new Error('Signable data does not match activity intent payload');
        }

        if (activity.activity.intent.signRawPayloadIntentV2?.signWith !== wallet.walletAddress) {
            throw new Error('Signable data encoding is not compatible with the wallet address');
        }

        try {
            const approveResult = await this.client.approveActivity({
                organizationId: user.subOrgId,
                fingerprint: activity.activity.fingerprint,
            });

            return {
                signature: {
                    r: '0x' + approveResult.signRawPayloadResult?.r,
                    s: '0x' + approveResult.signRawPayloadResult?.s,
                    v: approveResult.signRawPayloadResult?.v ? parseInt(approveResult.signRawPayloadResult.v, 16) : 0,
                },
            };
        } catch (error) {
            this.logger.error('Failed to sign raw payload', { error });
            throw new Error('Failed to sign raw payload');
        }
    }

    async loginByWalletV2(data: InputLoginWalletV2Dto): Promise<LoginV2DTO> {
        if (!data.url.includes('submit/stamp_login')) {
            throw new ApiError(TURNKEY_INVALID_ACTIVITY);
        }

        const response = await fetch(appConfig.TURNKEY_API_BASE_URL + data.url, {
            method: 'POST',
            body: JSON.stringify({
                parameters: {
                    publicKey: data.publicKey,
                    expirationSeconds: data.expirationSeconds,
                },
                organizationId: data.organizationId,
                timestampMs: data.timestampMs,
                type: 'ACTIVITY_TYPE_STAMP_LOGIN',
            }),
            headers: {
                [data.stampHeaderName]: data.stampHeaderValue,
            },
        });

        if (response.status != 200) {
            throw new ApiError(INVALID_SIGNATURE_ERROR);
        }

        const turnkeyResponse = (await response.json()) as TStampLoginResponse;

        const user = await this.userService.getUserBySubOrg(turnkeyResponse.activity.organizationId);
        if (!user) {
            throw new ApiError(USER_NOT_FOUND);
        }

        const payload: JwtClaim = {
            sub: user.id,
            iss: AUTH_AGENT,
        };

        return {
            accessToken: await this.jwtService.signAsync(payload),
            refreshToken: await this.jwtService.signAsync(payload, {
                secret: appConfig.JWT_REFRESH_SECRET,
                expiresIn: Number(data.expirationSeconds),
            }),
            userId: user.id,
            subOrgId: user.subOrgId ?? '',
            turnKeyResponse: turnkeyResponse.activity.result.stampLoginResult?.session ?? '',
        };
    }

    private validateEmail(email: string): boolean {
        const emailRegex = /^[^\s@]+@[^\s@]+\.[^\s@]+$/;
        return emailRegex.test(email);
    }

    async initOtpAuth(
        input: InitEmailOtpInputDTO,
        userIdentifier: string,
        requestIp: string,
    ): Promise<InitOtpAuthResponseDto> {
        try {
            // Validate email format if using email OTP
            if (!this.validateEmail(input.email)) {
                throw new ApiError(INVALID_EMAIL_FORMAT);
            }

            // Rate limiting by email
            const emailRateLimitResult = await this.rateLimitingService.checkRateLimitByIdentifier(input.email, {
                windowMs: EMAIL_RATE_LIMIT_WINDOW_MS,
                maxRequests: EMAIL_RATE_LIMIT_MAX_REQUESTS,
                keyPrefix: 'email_otp_init_by_email',
            });

            if (!emailRateLimitResult.allowed) {
                const waitTimeMinutes = Math.ceil((emailRateLimitResult.resetTime - Date.now()) / 60000);
                this.logger.warn('Email rate limit exceeded', {
                    email: input.email,
                    resetTime: new Date(emailRateLimitResult.resetTime).toISOString(),
                    waitTimeMinutes,
                });
                throw new ApiError(EMAIL_RATE_LIMITED);
            }

            // Rate limiting by fingerprint if provided
            if (input.fingerprint) {
                const fingerprintRateLimitResult = await this.rateLimitingService.checkRateLimitByIdentifier(
                    input.fingerprint,
                    {
                        windowMs: FINGERPRINT_RATE_LIMIT_WINDOW_MS,
                        maxRequests: FINGERPRINT_RATE_LIMIT_MAX_REQUESTS,
                        keyPrefix: 'email_otp_init_by_fingerprint',
                    },
                );

                if (!fingerprintRateLimitResult.allowed) {
                    const waitTimeMinutes = Math.ceil((fingerprintRateLimitResult.resetTime - Date.now()) / 60000);
                    this.logger.warn('Fingerprint rate limit exceeded', {
                        fingerprint: input.fingerprint,
                        resetTime: new Date(fingerprintRateLimitResult.resetTime).toISOString(),
                        waitTimeMinutes,
                    });
                    throw new ApiError(FINGERPRINT_RATE_LIMITED);
                }
            }

            // Check if email is throttled due to too many failed OTP verification attempts
            const otpThrottleByEmailResult = await this.rateLimitingService.checkOtpVerificationThrottleByEmail(
                input.email,
                {
                    windowMs: OTP_VERIFICATION_THROTTLE_WINDOW_MS,
                    maxRequests: OTP_VERIFICATION_THROTTLE_MAX_ATTEMPTS,
                    keyPrefix: 'otp_verification_throttle',
                },
            );

            if (!otpThrottleByEmailResult.allowed) {
                const waitTimeMinutes = Math.ceil((otpThrottleByEmailResult.resetTime - Date.now()) / 60000);
                this.logger.warn('Email OTP verification throttled by email', {
                    email: input.email,
                    resetTime: new Date(otpThrottleByEmailResult.resetTime).toISOString(),
                    waitTimeMinutes,
                    totalFailedAttempts: otpThrottleByEmailResult.totalHits,
                });

                const throttledError = { ...OTP_VERIFICATION_THROTTLED };
                throttledError.message = throttledError.message.replace('{waitTime}', `${waitTimeMinutes} minutes`);
                throw new ApiError(throttledError);
            }

            // Check if IP is throttled due to too many failed OTP verification attempts
            // This prevents users from bypassing email-based throttling by switching to different emails
            const otpThrottleByIpResult = await this.rateLimitingService.checkOtpVerificationThrottleByIp(requestIp, {
                windowMs: OTP_VERIFICATION_THROTTLE_WINDOW_MS,
                maxRequests: OTP_VERIFICATION_THROTTLE_MAX_ATTEMPTS,
                keyPrefix: 'otp_verification_throttle',
            });

            if (!otpThrottleByIpResult.allowed) {
                const waitTimeMinutes = Math.ceil((otpThrottleByIpResult.resetTime - Date.now()) / 60000);
                this.logger.warn('Email OTP init blocked due to IP throttling from failed verification attempts', {
                    requestIp: requestIp.substring(0, 8) + '***',
                    resetTime: new Date(otpThrottleByIpResult.resetTime).toISOString(),
                    waitTimeMinutes,
                    totalFailedAttempts: otpThrottleByIpResult.totalHits,
                    email: input.email.substring(0, 3) + '***',
                    reason: 'IP has too many failed OTP verification attempts, blocking initEmailOtp to prevent bypass via email switching',
                });

                const throttledError = { ...OTP_VERIFICATION_THROTTLED };
                throttledError.message = throttledError.message.replace('{waitTime}', `${waitTimeMinutes} minutes`);
                throw new ApiError(throttledError);
            }

            // Initialize OTP with Turnkey using the client instance
            const otpResponse = await this.client.initOtp({
                contact: input.email,
                otpType: OtpType.EMAIL,
                otpLength: TURNKEY_OTP_LENGTH,
                alphanumeric: TURNKEY_OTP_ALPHANUMERIC,
                userIdentifier: userIdentifier,
                expirationSeconds: String(TURNKEY_OTP_TTL),
                emailCustomization: {
                    appName: 'XBIT',
                },
            });

            if (!otpResponse?.otpId) {
                throw new ApiError(OTP_INIT_FAILED);
            }

            return {
                otpId: otpResponse.otpId,
                userId: '',
                subOrgId: '',
                ttl: TURNKEY_OTP_TTL,
            };
        } catch (error: any) {
            this.logger.error('Failed to initialize OTP authentication', {
                error: {
                    message: error?.message || 'Unknown error',
                    stack: error?.stack,
                    code: error?.code,
                    name: error?.name,
                    toString: error?.toString(),
                },
                input,
                availableMethods: Object.getOwnPropertyNames(this.client).filter(
                    (name) => typeof (this.client as any)[name] === 'function',
                ),
            });

            if (error instanceof ApiError) {
                throw error;
            }

            // Handle Turnkey-specific errors
            if (error?.code === 'RATE_LIMITED') {
                throw new ApiError(OTP_RATE_LIMITED);
            }

            throw new ApiError(OTP_INIT_FAILED);
        }
    }

    async loginWithEmailOtp(
        data: LoginWithEmailOtpInputDTO,
        userIdentifier?: string,
        requestIp?: string,
        turnkeyVersion: TurnkeyVersion = TurnkeyVersion.V1,
    ): Promise<LoginEmailOtpDTO> {
        const { email, otpId, otpCode, targetPublicKey } = data;

        // todo: remove demo account
        if (email === '<EMAIL>' && otpCode === '123456' && appConfig.STAGE !== 'prod') {
            const { user, isNewUser } = await this.userService.getOrCreateUserByEmail({
                email: data.email,
                // referrerCode: data.referrerCode,
                // fingerprint: data.fingerprint,
            });

            if (!user) {
                throw new ApiError(USER_NOT_FOUND);
            }

            if (!user.subOrgId) {
                const userName = `email|${email}`;
                const { subOrgId, userId, wallets } = await this.createUserSubOrg(
                    userName,
                    [], // No OAuth providers for email OTP
                    [], // No API keys initially
                    `email|${email}`,
                    email,
                    undefined,
                    turnkeyVersion,
                );

                user.subOrgId = subOrgId;
                user.turnkeyRootUserId = userId;

                await this.userService.updateUser(user);
                await this.userService.createUserEmbeddedWallets(user, wallets);
            }

            const turnkeyClient = new TurnkeyClient(
                { baseUrl: 'https://api.turnkey.com' },
                new ApiKeyStamper({
                    apiPublicKey: appConfig.TURNKEY_FAKE_PUBLIC_KEY,
                    apiPrivateKey: appConfig.TURNKEY_FAKE_PRIVATE_KEY,
                }),
            );

            const login = await turnkeyClient.stampLogin({
                type: 'ACTIVITY_TYPE_STAMP_LOGIN',
                parameters: {
                    publicKey: targetPublicKey,
                    expirationSeconds: '604800', // 7 days
                },
                organizationId: user.subOrgId ?? '',
                timestampMs: Date.now().toString(),
            });

            const payload: JwtClaim = {
                sub: user.id,
                iss: AUTH_AGENT,
            };

            return {
                accessToken: await this.jwtService.signAsync(payload),
                refreshToken: await this.jwtService.signAsync(payload, {
                    secret: appConfig.JWT_REFRESH_SECRET,
                    expiresIn: appConfig.REFRESH_TOKEN_EXPIRES_IN,
                }),
                userId: user.id,
                subOrgId: user.subOrgId ?? '',
                turnKeyResponse: {
                    session: login.activity.result.stampLoginResult?.session ?? '',
                },
            };
        }

        // Check OTP verification throttling to prevent brute force attacks
        const throttleResult = await this.rateLimitingService.checkOtpVerificationThrottle(otpId, {
            windowMs: OTP_VERIFICATION_THROTTLE_WINDOW_MS,
            maxRequests: OTP_VERIFICATION_THROTTLE_MAX_ATTEMPTS,
            keyPrefix: 'otp_verification_throttle',
        });

        if (!throttleResult.allowed) {
            const waitTimeMinutes = Math.ceil((throttleResult.resetTime - Date.now()) / 60000);
            this.logger.warn('OTP verification throttled', {
                otpId,
                resetTime: new Date(throttleResult.resetTime).toISOString(),
                waitTimeMinutes,
                totalAttempts: throttleResult.totalHits,
            });

            const throttledError = { ...OTP_VERIFICATION_THROTTLED };
            throttledError.message = throttledError.message.replace('{waitTime}', `${waitTimeMinutes} minutes`);
            throw new ApiError(throttledError);
        }

        try {
            const verifyResponse = await this.client.verifyOtp({
                otpId,
                otpCode,
                expirationSeconds: String(TURNKEY_OTP_TTL),
            });

            if (!verifyResponse?.verificationToken) {
                // Record failed attempt for throttling by both otpId and email
                await this.rateLimitingService.recordFailedOtpVerification(otpId, {
                    windowMs: OTP_VERIFICATION_THROTTLE_WINDOW_MS,
                    maxRequests: OTP_VERIFICATION_THROTTLE_MAX_ATTEMPTS,
                    keyPrefix: 'otp_verification_throttle',
                });
                await this.rateLimitingService.recordFailedOtpVerificationByEmail(email, {
                    windowMs: OTP_VERIFICATION_THROTTLE_WINDOW_MS,
                    maxRequests: OTP_VERIFICATION_THROTTLE_MAX_ATTEMPTS,
                    keyPrefix: 'otp_verification_throttle',
                });
                if (requestIp) {
                    await this.rateLimitingService.recordFailedOtpVerificationByIp(requestIp, {
                        windowMs: OTP_VERIFICATION_THROTTLE_WINDOW_MS,
                        maxRequests: OTP_VERIFICATION_THROTTLE_MAX_ATTEMPTS,
                        keyPrefix: 'otp_verification_throttle',
                    });
                }
                throw new Error('No verification token received from OTP verification');
            }

            // const user = await this.userService.getUserByEmail(email);
            const { user, isNewUser } = await this.userService.getOrCreateUserByEmail({
                email: data.email,
                // referrerCode: data.referrerCode,
                // fingerprint: data.fingerprint,
                turnkeyVersion,
            });

            if (!user) {
                throw new ApiError(USER_NOT_FOUND);
            }

            if (!user.subOrgId) {
                const userName = `email|${email}`;
                const { subOrgId, userId, wallets, privateKey, publicKey } = await this.createUserSubOrg(
                    userName,
                    [], // No OAuth providers for email OTP
                    [], // No API keys initially
                    `email|${email}`,
                    email,
                    undefined, // No OIDC token for email OTP
                    turnkeyVersion,
                );

                user.subOrgId = subOrgId;
                user.turnkeyRootUserId = userId;

                await this.userService.updateUser(user);
                await this.userService.createUserEmbeddedWallets(user, wallets);

                if (turnkeyVersion === TurnkeyVersion.V2) {
                    await this.userService.createTurnKeySubOrganizationAuth(user, {
                        subOrgId,
                        privateKey: privateKey ?? '',
                        publicKey: publicKey ?? '',
                    });
                }
            }

            // Step 4: Use verification token to complete login
            const loginResponse = await this.client.otpLogin({
                organizationId: user.subOrgId ?? '',
                verificationToken: verifyResponse.verificationToken,
                publicKey: targetPublicKey,
                expirationSeconds: '604800', // 7 days
            });

            if (!loginResponse.session) {
                throw new Error('No session received from OTP login');
            }

            // Clean up the mapping
            const payload: JwtClaim = {
                sub: user.id,
                iss: AUTH_AGENT,
            };

            // Clear rate limits after successful authentication to allow immediate re-login with different email
            if (userIdentifier) {
                // userIdentifier is the original IP address from the resolver
                await this.rateLimitingService.clearEmailOtpRateLimits(email, userIdentifier);
            }

            return {
                accessToken: await this.jwtService.signAsync(payload),
                refreshToken: await this.jwtService.signAsync(payload, {
                    secret: appConfig.JWT_REFRESH_SECRET,
                    expiresIn: appConfig.REFRESH_TOKEN_EXPIRES_IN,
                }),
                userId: user.id,
                subOrgId: user.subOrgId ?? '',
                turnKeyResponse: {
                    session: loginResponse.session,
                },
            };
        } catch (error: any) {
            this.logger.error('Failed to login with email OTP', {
                error: {
                    message: error?.message || 'Unknown error',
                    name: error?.name,
                },
                otpId,
            });

            if (error instanceof ApiError) {
                throw error;
            }

            // Record failed OTP verification attempt for throttling by otpId, email, and IP
            await this.rateLimitingService.recordFailedOtpVerification(otpId, {
                windowMs: OTP_VERIFICATION_THROTTLE_WINDOW_MS,
                maxRequests: OTP_VERIFICATION_THROTTLE_MAX_ATTEMPTS,
                keyPrefix: 'otp_verification_throttle',
            });
            await this.rateLimitingService.recordFailedOtpVerificationByEmail(email, {
                windowMs: OTP_VERIFICATION_THROTTLE_WINDOW_MS,
                maxRequests: OTP_VERIFICATION_THROTTLE_MAX_ATTEMPTS,
                keyPrefix: 'otp_verification_throttle',
            });
            if (requestIp) {
                await this.rateLimitingService.recordFailedOtpVerificationByIp(requestIp, {
                    windowMs: OTP_VERIFICATION_THROTTLE_WINDOW_MS,
                    maxRequests: OTP_VERIFICATION_THROTTLE_MAX_ATTEMPTS,
                    keyPrefix: 'otp_verification_throttle',
                });
            }

            throw new ApiError(OTP_VERIFICATION_FAILED);
        }
    }

    async approveExportPassphrase(
        userId: string,
        input: ExportPassphraseInput,
        requestIp?: string,
    ): Promise<ApprovedPassphraseResponse> {
        try {
            const user = await this.userService.getUserById(userId);

            if (!user || !user.subOrgId) {
                throw new ApiError(USER_NOT_FOUND);
            }

            if (user.isExportedWallet) {
                throw new ApiError(EXPORT_PASSPHRASE_ALREADY_CREATED);
            }

            // Check if user email is throttled due to too many failed OTP verification attempts
            if (user.email && input.otpId) {
                const otpThrottleResult = await this.rateLimitingService.checkOtpVerificationThrottleByEmail(
                    user.email,
                    {
                        windowMs: OTP_VERIFICATION_THROTTLE_WINDOW_MS,
                        maxRequests: OTP_VERIFICATION_THROTTLE_MAX_ATTEMPTS,
                        keyPrefix: 'otp_verification_throttle',
                    },
                );

                if (!otpThrottleResult.allowed) {
                    const waitTimeMinutes = Math.ceil((otpThrottleResult.resetTime - Date.now()) / 60000);
                    this.logger.warn('Export passphrase blocked due to OTP verification throttling by email', {
                        email: user.email,
                        resetTime: new Date(otpThrottleResult.resetTime).toISOString(),
                        waitTimeMinutes,
                        totalFailedAttempts: otpThrottleResult.totalHits,
                    });

                    const throttledError = { ...OTP_VERIFICATION_THROTTLED };
                    throttledError.message = throttledError.message.replace('{waitTime}', `${waitTimeMinutes} minutes`);
                    throw new ApiError(throttledError);
                }
            }

            // Check if IP is throttled due to too many failed OTP verification attempts
            if (requestIp && input.otpId) {
                const otpThrottleByIpResult = await this.rateLimitingService.checkOtpVerificationThrottleByIp(
                    requestIp,
                    {
                        windowMs: OTP_VERIFICATION_THROTTLE_WINDOW_MS,
                        maxRequests: OTP_VERIFICATION_THROTTLE_MAX_ATTEMPTS,
                        keyPrefix: 'otp_verification_throttle',
                    },
                );

                if (!otpThrottleByIpResult.allowed) {
                    const waitTimeMinutes = Math.ceil((otpThrottleByIpResult.resetTime - Date.now()) / 60000);
                    this.logger.warn('Export passphrase blocked due to OTP verification throttling by IP', {
                        requestIp: requestIp.substring(0, 8) + '***',
                        resetTime: new Date(otpThrottleByIpResult.resetTime).toISOString(),
                        waitTimeMinutes,
                        totalFailedAttempts: otpThrottleByIpResult.totalHits,
                    });

                    const throttledError = { ...OTP_VERIFICATION_THROTTLED };
                    throttledError.message = throttledError.message.replace('{waitTime}', `${waitTimeMinutes} minutes`);
                    throw new ApiError(throttledError);
                }
            }

            // Skip verification for first-time login users
            if (!user.isFirstLogin) {
                const verifiedUser = await this.reverifyUserAuthentication(
                    user,
                    {
                        message: input.message,
                        signature: input.signature,
                        isOkxWallet: input.isOkxWallet,
                        oidcToken: input.oidcToken,
                        otpId: input.otpId,
                        otpCode: input.otpCode,
                    },
                    requestIp,
                );

                if (!verifiedUser) {
                    throw new ApiError(INVALID_USER_VERIFICATION);
                }
            }

            const wallets = await this.userService.getUserEmbeddedWalletsWithoutBalance(user);

            if (!wallets.length) {
                throw new ApiError(EMBEDDED_WALLET_NOT_FOUND);
            }

            const response = await this.client.getActivity({
                organizationId: user.subOrgId,
                activityId: input.activityId,
            });

            if (response?.activity?.type !== 'ACTIVITY_TYPE_EXPORT_WALLET') {
                throw new ApiError(TURNKEY_INVALID_ACTIVITY);
            }

            if (!response.activity.intent?.exportWalletIntent) {
                throw new ApiError(TURNKEY_INVALID_ACTIVITY);
            }

            if (response.activity.intent.exportWalletIntent?.targetPublicKey !== input.publicKey) {
                throw new ApiError(TURNKEY_INVALID_ACTIVITY);
            }

            if (response.activity.intent.exportWalletIntent?.walletId !== wallets[0].walletId) {
                throw new ApiError(TURNKEY_INVALID_ACTIVITY);
            }

            const approveActivity = await this.client.approveActivity({
                organizationId: user.subOrgId,
                fingerprint: response.activity.fingerprint,
            });

            if (approveActivity?.activity?.failure) {
                this.logger.error('Failed to approve export passphrase activity', {
                    activityId: input.activityId,
                    error: approveActivity.activity.failure,
                });

                throw new ApiError(EXPORT_PASSPHRASE_FAILED);
            }

            // If this was a first-time login user, mark as no longer first login
            if (user.isFirstLogin) {
                await this.userService.updateUserFirstLoginStatus(userId, false);
            }

            // Clear export rate limits and OTP verification throttle on successful export
            if (requestIp) {
                try {
                    await this.rateLimitingService.resetRateLimit(requestIp, {
                        windowMs: RATE_LIMIT_WINDOW_MS,
                        maxRequests: EXPORT_PASSPHRASE_MAX_REQUESTS,
                        keyPrefix: 'export_passphrase',
                    });
                    this.logger.info(
                        {
                            userId,
                            requestIp: requestIp.substring(0, 8) + '***', // Partially masked IP for logging
                        },
                        'Export passphrase rate limit cleared after successful export',
                    );
                } catch (error) {
                    this.logger.warn('Failed to clear export passphrase rate limit', { error });
                }
            }

            // Clear OTP verification throttle by email and IP on successful export
            if (user.email) {
                await this.rateLimitingService.clearOtpVerificationThrottleByEmail(user.email);
                this.logger.info(
                    {
                        userId,
                        email: user.email,
                    },
                    'OTP verification throttle by email cleared after successful export passphrase',
                );
            }
            if (requestIp) {
                await this.rateLimitingService.clearOtpVerificationThrottleByIp(requestIp);
                this.logger.info(
                    {
                        userId,
                        requestIp: requestIp.substring(0, 8) + '***',
                    },
                    'OTP verification throttle by IP cleared after successful export passphrase',
                );
            }

            return {
                passphrase: approveActivity.exportWalletResult?.exportBundle ?? '',
                activityId: input.activityId,
            };
        } catch (error) {
            if (error instanceof ApiError) {
                throw error;
            }

            this.logger.error('Failed to create export pass phase', { error });
            throw new ApiError(EXPORT_PASSPHRASE_FAILED);
        }
    }

    async updateUserExportedWallet(userId: string): Promise<boolean> {
        const user = await this.userService.getUserById(userId);

        if (!user) {
            throw new ApiError(USER_NOT_FOUND);
        }

        if (user.isExportedWallet) {
            throw new ApiError(EXPORT_PASSPHRASE_ALREADY_CREATED);
        }

        try {
            user.isExportedWallet = true;
            await this.userService.updateUser(user);
            return true;
        } catch (error) {
            this.logger.error({ error }, 'Failed to update user exported wallet status');
            throw new ApiError(EXPORT_PASSPHRASE_ALREADY_CREATED);
        }
    }

    async requestReverifyOtp(
        userId: any,
        input: RequestReverifyOtpInputDto,
        requestIp?: string,
    ): Promise<InitOtpAuthResponseDto> {
        const user = await this.userService.getUserById(userId);
        if (!user || !user.subOrgId) {
            throw new ApiError(USER_NOT_FOUND);
        }

        if (!user.email || !this.validateEmail(user.email)) {
            throw new ApiError(INVALID_EMAIL_FORMAT);
        }

        // Rate limiting by email (similar to initEmailOtp)
        const emailRateLimitResult = await this.rateLimitingService.checkRateLimitByIdentifier(user.email, {
            windowMs: EMAIL_RATE_LIMIT_WINDOW_MS,
            maxRequests: EMAIL_RATE_LIMIT_MAX_REQUESTS,
            keyPrefix: 'request_reverify_otp_by_email',
        });

        if (!emailRateLimitResult.allowed) {
            const waitTimeMinutes = Math.ceil((emailRateLimitResult.resetTime - Date.now()) / 60000);
            this.logger.warn('Email rate limit exceeded for request reverify OTP', {
                email: user.email,
                resetTime: new Date(emailRateLimitResult.resetTime).toISOString(),
                waitTimeMinutes,
            });
            throw new ApiError(EMAIL_RATE_LIMITED);
        }

        // Rate limiting by fingerprint if provided (similar to initEmailOtp)
        if (input.fingerprint) {
            const fingerprintRateLimitResult = await this.rateLimitingService.checkRateLimitByIdentifier(
                input.fingerprint,
                {
                    windowMs: FINGERPRINT_RATE_LIMIT_WINDOW_MS,
                    maxRequests: FINGERPRINT_RATE_LIMIT_MAX_REQUESTS,
                    keyPrefix: 'request_reverify_otp_by_fingerprint',
                },
            );

            if (!fingerprintRateLimitResult.allowed) {
                const waitTimeMinutes = Math.ceil((fingerprintRateLimitResult.resetTime - Date.now()) / 60000);
                this.logger.warn('Fingerprint rate limit exceeded for request reverify OTP', {
                    fingerprint: input.fingerprint,
                    resetTime: new Date(fingerprintRateLimitResult.resetTime).toISOString(),
                    waitTimeMinutes,
                });
                throw new ApiError(FINGERPRINT_RATE_LIMITED);
            }
        }

        // Check if user email is throttled due to too many failed OTP verification attempts
        const otpThrottleByEmailResult = await this.rateLimitingService.checkOtpVerificationThrottleByEmail(
            user.email,
            {
                windowMs: OTP_VERIFICATION_THROTTLE_WINDOW_MS,
                maxRequests: OTP_VERIFICATION_THROTTLE_MAX_ATTEMPTS,
                keyPrefix: 'otp_verification_throttle',
            },
        );

        if (!otpThrottleByEmailResult.allowed) {
            const waitTimeMinutes = Math.ceil((otpThrottleByEmailResult.resetTime - Date.now()) / 60000);
            this.logger.warn('Request reverify OTP blocked due to OTP verification throttling by email', {
                email: user.email,
                resetTime: new Date(otpThrottleByEmailResult.resetTime).toISOString(),
                waitTimeMinutes,
                totalFailedAttempts: otpThrottleByEmailResult.totalHits,
            });

            const throttledError = { ...OTP_VERIFICATION_THROTTLED };
            throttledError.message = throttledError.message.replace('{waitTime}', `${waitTimeMinutes} minutes`);
            throw new ApiError(throttledError);
        }

        // Check if IP is throttled due to too many failed OTP verification attempts
        if (requestIp) {
            const otpThrottleByIpResult = await this.rateLimitingService.checkOtpVerificationThrottleByIp(requestIp, {
                windowMs: OTP_VERIFICATION_THROTTLE_WINDOW_MS,
                maxRequests: OTP_VERIFICATION_THROTTLE_MAX_ATTEMPTS,
                keyPrefix: 'otp_verification_throttle',
            });

            if (!otpThrottleByIpResult.allowed) {
                const waitTimeMinutes = Math.ceil((otpThrottleByIpResult.resetTime - Date.now()) / 60000);
                this.logger.warn('Request reverify OTP blocked due to OTP verification throttling by IP', {
                    requestIp: requestIp.substring(0, 8) + '***',
                    resetTime: new Date(otpThrottleByIpResult.resetTime).toISOString(),
                    waitTimeMinutes,
                    totalFailedAttempts: otpThrottleByIpResult.totalHits,
                });

                const throttledError = { ...OTP_VERIFICATION_THROTTLED };
                throttledError.message = throttledError.message.replace('{waitTime}', `${waitTimeMinutes} minutes`);
                throw new ApiError(throttledError);
            }
        }

        try {
            const otpResponse = await this.client.initOtp({
                contact: user.email,
                otpType: OtpType.EMAIL,
                otpLength: TURNKEY_OTP_LENGTH,
                alphanumeric: TURNKEY_OTP_ALPHANUMERIC,
                userIdentifier: input.fingerprint,
                expirationSeconds: String(TURNKEY_OTP_TTL),
            });

            if (!otpResponse?.otpId) {
                throw new ApiError(OTP_INIT_FAILED);
            }

            // Clear rate limits on successful OTP request (similar to initEmailOtp)
            if (requestIp && user.email) {
                try {
                    // Clear IP-based rate limit
                    await this.rateLimitingService.resetRateLimit(requestIp, {
                        windowMs: RATE_LIMIT_WINDOW_MS,
                        maxRequests: EXPORT_PASSPHRASE_MAX_REQUESTS,
                        keyPrefix: 'request_reverify_otp',
                    });

                    // Clear email-based rate limit
                    await this.rateLimitingService.resetRateLimitByIdentifier(user.email, {
                        windowMs: EMAIL_RATE_LIMIT_WINDOW_MS,
                        maxRequests: EMAIL_RATE_LIMIT_MAX_REQUESTS,
                        keyPrefix: 'request_reverify_otp_by_email',
                    });

                    // Clear fingerprint-based rate limit if provided
                    if (input.fingerprint) {
                        await this.rateLimitingService.resetRateLimitByIdentifier(input.fingerprint, {
                            windowMs: FINGERPRINT_RATE_LIMIT_WINDOW_MS,
                            maxRequests: FINGERPRINT_RATE_LIMIT_MAX_REQUESTS,
                            keyPrefix: 'request_reverify_otp_by_fingerprint',
                        });
                    }

                    this.logger.info(
                        {
                            userId: user.id,
                            requestIp: requestIp.substring(0, 8) + '***', // Partially masked IP for logging
                        },
                        'Request reverify OTP rate limits cleared after successful request',
                    );
                } catch (error) {
                    this.logger.warn('Failed to clear request reverify OTP rate limits', { error });
                }
            }

            return {
                otpId: otpResponse.otpId,
                userId: user.id,
                subOrgId: user.subOrgId ?? '',
                ttl: TURNKEY_OTP_TTL,
            };
        } catch (error) {
            this.logger.error('Failed to request reverify OTP', { error });
            throw new ApiError(OTP_INIT_FAILED);
        }
    }

    async reverifyUserAuthentication(
        user: User,
        params: ReverifyUserAuthenticationDto,
        requestIp?: string,
    ): Promise<boolean> {
        if ([AuthProvider.GOOGLE, AuthProvider.EMAIL].includes(user.authProvider)) {
            // verify oidcToken
            if (params.oidcToken) {
                const googleUser = await this.googleAuthService.verifyGoogleToken(params.oidcToken);

                if (googleUser.email !== user.email) {
                    throw new ApiError(INVALID_GOOGLE_LOGIN);
                }

                return true;
            }

            if (params.otpId) {
                if (!params.otpCode) {
                    throw new ApiError(OTP_VERIFICATION_FAILED);
                }

                // Check OTP verification throttling to prevent brute force attacks
                const throttleResult = await this.rateLimitingService.checkOtpVerificationThrottle(params.otpId, {
                    windowMs: OTP_VERIFICATION_THROTTLE_WINDOW_MS,
                    maxRequests: OTP_VERIFICATION_THROTTLE_MAX_ATTEMPTS,
                    keyPrefix: 'otp_verification_throttle',
                });

                if (!throttleResult.allowed) {
                    const waitTimeMinutes = Math.ceil((throttleResult.resetTime - Date.now()) / 60000);
                    this.logger.warn('OTP verification throttled in reverifyUserAuthentication', {
                        otpId: params.otpId,
                        resetTime: new Date(throttleResult.resetTime).toISOString(),
                        waitTimeMinutes,
                        totalAttempts: throttleResult.totalHits,
                    });

                    const throttledError = { ...OTP_VERIFICATION_THROTTLED };
                    throttledError.message = throttledError.message.replace('{waitTime}', `${waitTimeMinutes} minutes`);
                    throw new ApiError(throttledError);
                }

                try {
                    const otpResponse = await this.client.verifyOtp({
                        otpId: params.otpId,
                        otpCode: params.otpCode,
                        expirationSeconds: String(TURNKEY_OTP_TTL),
                    });

                    if (!otpResponse?.verificationToken) {
                        // Record failed attempt for throttling by otpId, email, and IP
                        await this.rateLimitingService.recordFailedOtpVerification(params.otpId, {
                            windowMs: OTP_VERIFICATION_THROTTLE_WINDOW_MS,
                            maxRequests: OTP_VERIFICATION_THROTTLE_MAX_ATTEMPTS,
                            keyPrefix: 'otp_verification_throttle',
                        });
                        if (user.email) {
                            await this.rateLimitingService.recordFailedOtpVerificationByEmail(user.email, {
                                windowMs: OTP_VERIFICATION_THROTTLE_WINDOW_MS,
                                maxRequests: OTP_VERIFICATION_THROTTLE_MAX_ATTEMPTS,
                                keyPrefix: 'otp_verification_throttle',
                            });
                        }
                        if (requestIp) {
                            await this.rateLimitingService.recordFailedOtpVerificationByIp(requestIp, {
                                windowMs: OTP_VERIFICATION_THROTTLE_WINDOW_MS,
                                maxRequests: OTP_VERIFICATION_THROTTLE_MAX_ATTEMPTS,
                                keyPrefix: 'otp_verification_throttle',
                            });
                        }
                        throw new ApiError(OTP_VERIFICATION_FAILED);
                    }

                    return true;
                } catch (error) {
                    // Record failed attempt for throttling if it's not already an ApiError
                    if (!(error instanceof ApiError)) {
                        await this.rateLimitingService.recordFailedOtpVerification(params.otpId, {
                            windowMs: OTP_VERIFICATION_THROTTLE_WINDOW_MS,
                            maxRequests: OTP_VERIFICATION_THROTTLE_MAX_ATTEMPTS,
                            keyPrefix: 'otp_verification_throttle',
                        });
                        if (user.email) {
                            await this.rateLimitingService.recordFailedOtpVerificationByEmail(user.email, {
                                windowMs: OTP_VERIFICATION_THROTTLE_WINDOW_MS,
                                maxRequests: OTP_VERIFICATION_THROTTLE_MAX_ATTEMPTS,
                                keyPrefix: 'otp_verification_throttle',
                            });
                        }
                        if (requestIp) {
                            await this.rateLimitingService.recordFailedOtpVerificationByIp(requestIp, {
                                windowMs: OTP_VERIFICATION_THROTTLE_WINDOW_MS,
                                maxRequests: OTP_VERIFICATION_THROTTLE_MAX_ATTEMPTS,
                                keyPrefix: 'otp_verification_throttle',
                            });
                        }
                    }
                    throw error;
                }
            }
        }

        if (!params.message || !params.signature) {
            throw new ApiError(INVALID_SIGNATURE_ERROR);
        }

        const walletAddress = params.isOkxWallet
            ? this.walletAuthService.extractOkxWalletAddress(params.message)
            : this.walletAuthService.extractWalletAddress(params.message);

        if (walletAddress !== user.walletAddress) {
            throw new ApiError(INVALID_SIGNATURE_ERROR);
        }

        switch (user.authProvider) {
            case AuthProvider.CHAIN_EVM:
                if (!this.walletAuthService.verifyEVMSignature(params.message, params.signature, walletAddress)) {
                    throw new ApiError(INVALID_SIGNATURE_ERROR);
                }
                break;
            case AuthProvider.CHAIN_SOL:
                if (!this.walletAuthService.verifySolSignature(params.message, params.signature, walletAddress)) {
                    throw new ApiError(INVALID_SIGNATURE_ERROR);
                }
                break;
            case AuthProvider.CHAIN_TRON: {
                const isValid = await this.walletAuthService.verifyTrxSignature(
                    params.message,
                    params.signature,
                    walletAddress,
                );
                if (!isValid) {
                    throw new ApiError(INVALID_SIGNATURE_ERROR);
                }
                break;
            }
            default:
                throw new ApiError(CHAIN_TYPE_NOT_SUPPORTED);
        }

        return true;
    }

    async approveExportAccountPrivateKey(
        userId: string,
        input: ExportPrivateKeyInput,
        requestIp?: string,
    ): Promise<ApprovedPrivateKeyResponse> {
        try {
            const user = await this.userService.getUserById(userId);

            if (!user || !user.subOrgId) {
                throw new ApiError(USER_NOT_FOUND);
            }

            // Check if user email is throttled due to too many failed OTP verification attempts
            if (user.email && input.otpId) {
                const otpThrottleResult = await this.rateLimitingService.checkOtpVerificationThrottleByEmail(
                    user.email,
                    {
                        windowMs: OTP_VERIFICATION_THROTTLE_WINDOW_MS,
                        maxRequests: OTP_VERIFICATION_THROTTLE_MAX_ATTEMPTS,
                        keyPrefix: 'otp_verification_throttle',
                    },
                );

                if (!otpThrottleResult.allowed) {
                    const waitTimeMinutes = Math.ceil((otpThrottleResult.resetTime - Date.now()) / 60000);
                    this.logger.warn('Export private key blocked due to OTP verification throttling by email', {
                        email: user.email,
                        resetTime: new Date(otpThrottleResult.resetTime).toISOString(),
                        waitTimeMinutes,
                        totalFailedAttempts: otpThrottleResult.totalHits,
                    });

                    const throttledError = { ...OTP_VERIFICATION_THROTTLED };
                    throttledError.message = throttledError.message.replace('{waitTime}', `${waitTimeMinutes} minutes`);
                    throw new ApiError(throttledError);
                }
            }

            // Check if IP is throttled due to too many failed OTP verification attempts
            if (requestIp && input.otpId) {
                const otpThrottleByIpResult = await this.rateLimitingService.checkOtpVerificationThrottleByIp(
                    requestIp,
                    {
                        windowMs: OTP_VERIFICATION_THROTTLE_WINDOW_MS,
                        maxRequests: OTP_VERIFICATION_THROTTLE_MAX_ATTEMPTS,
                        keyPrefix: 'otp_verification_throttle',
                    },
                );

                if (!otpThrottleByIpResult.allowed) {
                    const waitTimeMinutes = Math.ceil((otpThrottleByIpResult.resetTime - Date.now()) / 60000);
                    this.logger.warn('Export private key blocked due to OTP verification throttling by IP', {
                        requestIp: requestIp.substring(0, 8) + '***',
                        resetTime: new Date(otpThrottleByIpResult.resetTime).toISOString(),
                        waitTimeMinutes,
                        totalFailedAttempts: otpThrottleByIpResult.totalHits,
                    });

                    const throttledError = { ...OTP_VERIFICATION_THROTTLED };
                    throttledError.message = throttledError.message.replace('{waitTime}', `${waitTimeMinutes} minutes`);
                    throw new ApiError(throttledError);
                }
            }

            const verifiedUser = await this.reverifyUserAuthentication(
                user,
                {
                    message: input.message,
                    signature: input.signature,
                    isOkxWallet: input.isOkxWallet,
                    oidcToken: input.oidcToken,
                    otpId: input.otpId,
                    otpCode: input.otpCode,
                },
                requestIp,
            );

            if (!verifiedUser) {
                throw new ApiError(INVALID_USER_VERIFICATION);
            }

            const wallets = await this.userService.getUserEmbeddedWalletsWithoutBalance(user);

            if (!wallets.length) {
                throw new ApiError(EMBEDDED_WALLET_NOT_FOUND);
            }

            const response = await this.client.getActivity({
                organizationId: user.subOrgId,
                activityId: input.activityId,
            });

            if (response?.activity?.type !== 'ACTIVITY_TYPE_EXPORT_WALLET_ACCOUNT') {
                throw new ApiError(TURNKEY_INVALID_ACTIVITY);
            }

            if (!response.activity.intent?.exportWalletAccountIntent) {
                throw new ApiError(TURNKEY_INVALID_ACTIVITY);
            }

            if (response.activity.intent.exportWalletAccountIntent?.targetPublicKey !== input.publicKey) {
                throw new ApiError(TURNKEY_INVALID_ACTIVITY);
            }

            if (
                !wallets.find(
                    (wallet) => wallet.walletAddress === response.activity.intent.exportWalletAccountIntent?.address,
                )
            ) {
                throw new ApiError(TURNKEY_INVALID_ACTIVITY);
            }

            const approveActivity = await this.client.approveActivity({
                organizationId: user.subOrgId,
                fingerprint: response.activity.fingerprint,
            });

            if (approveActivity?.activity?.failure) {
                this.logger.error('Failed to approve export passphrase activity', {
                    activityId: input.activityId,
                    error: approveActivity.activity.failure,
                });

                throw new ApiError(EXPORT_PRIVATE_KEY_FAILED);
            }

            // Clear export rate limits on successful export
            if (requestIp) {
                try {
                    await this.rateLimitingService.resetRateLimit(requestIp, {
                        windowMs: RATE_LIMIT_WINDOW_MS,
                        maxRequests: EXPORT_PRIVATE_KEY_MAX_REQUESTS,
                        keyPrefix: 'export_private_key',
                    });
                    this.logger.info(
                        {
                            userId,
                            requestIp: requestIp.substring(0, 8) + '***', // Partially masked IP for logging
                        },
                        'Export private key rate limit cleared after successful export',
                    );
                } catch (error) {
                    this.logger.warn('Failed to clear export private key rate limit', { error });
                }
            }

            // Clear OTP verification throttle by email and IP on successful export
            if (user.email) {
                await this.rateLimitingService.clearOtpVerificationThrottleByEmail(user.email);
                this.logger.info(
                    {
                        userId,
                        email: user.email,
                    },
                    'OTP verification throttle by email cleared after successful export private key',
                );
            }
            if (requestIp) {
                await this.rateLimitingService.clearOtpVerificationThrottleByIp(requestIp);
                this.logger.info(
                    {
                        userId,
                        requestIp: requestIp.substring(0, 8) + '***',
                    },
                    'OTP verification throttle by IP cleared after successful export private key',
                );
            }

            return {
                privateKey: approveActivity.exportWalletAccountResult?.exportBundle ?? '',
                activityId: input.activityId,
            };
        } catch (error) {
            if (error instanceof ApiError) {
                throw error;
            }

            this.logger.error('Failed to approve export account private key', { error });
            throw new ApiError(EXPORT_PRIVATE_KEY_FAILED);
        }
    }

    async approveExportPrivateKeyWithoutVerify(
        userId: string,
        input: ExportPrivateKeyWithoutVerifyInput,
    ): Promise<ApprovedPrivateKeyResponse> {
        try {
            const user = await this.userService.getUserById(userId);

            if (!user || !user.subOrgId) {
                throw new ApiError(USER_NOT_FOUND);
            }

            const wallets = await this.userService.getUserEmbeddedWalletsWithoutBalance(user);

            if (!wallets.length) {
                throw new ApiError(EMBEDDED_WALLET_NOT_FOUND);
            }

            const response = await this.client.getActivity({
                organizationId: user.subOrgId,
                activityId: input.activityId,
            });

            if (response?.activity?.type !== 'ACTIVITY_TYPE_EXPORT_WALLET_ACCOUNT') {
                throw new ApiError(TURNKEY_INVALID_ACTIVITY);
            }

            if (!response.activity.intent?.exportWalletAccountIntent) {
                throw new ApiError(TURNKEY_INVALID_ACTIVITY);
            }

            if (response.activity.intent.exportWalletAccountIntent?.targetPublicKey !== input.publicKey) {
                throw new ApiError(TURNKEY_INVALID_ACTIVITY);
            }

            if (
                !wallets.find(
                    (wallet) => wallet.walletAddress === response.activity.intent.exportWalletAccountIntent?.address,
                )
            ) {
                throw new ApiError(TURNKEY_INVALID_ACTIVITY);
            }

            const approveActivity = await this.client.approveActivity({
                organizationId: user.subOrgId,
                fingerprint: response.activity.fingerprint,
            });

            if (approveActivity?.activity?.failure) {
                this.logger.error('Failed to approve export private key activity without verify', {
                    activityId: input.activityId,
                    error: approveActivity.activity.failure,
                });

                throw new ApiError(EXPORT_PRIVATE_KEY_FAILED);
            }

            return {
                privateKey: approveActivity.exportWalletAccountResult?.exportBundle ?? '',
                activityId: input.activityId,
            };
        } catch (error) {
            if (error instanceof ApiError) {
                throw error;
            }

            this.logger.error('Failed to approve export private key without verify', { error });
            throw new ApiError(EXPORT_PRIVATE_KEY_FAILED);
        }
    }

    async approveCreateWallet(userId: string, input: ApproveCreateWalletInput): Promise<ApprovedCreateWalletResponse> {
        try {
            const user = await this.userService.getUserById(userId);

            if (!user || !user.subOrgId) {
                throw new ApiError(USER_NOT_FOUND);
            }

            const wallets = await this.userService.getUserEmbeddedWalletsWithoutBalance(user);

            if (!wallets.length) {
                throw new ApiError(EMBEDDED_WALLET_NOT_FOUND);
            }

            const solanaWallets = wallets.filter((wallet) => wallet.chain === ChainType.SOLANA);
            // todo: add redis distributed lock to prevent race conditions
            if (solanaWallets.length >= MAX_SOLANA_WALLET_ACCOUNTS) {
                throw new ApiError(SOLANA_WALLET_LIMIT_REACHED);
            }

            // Check if wallet name already exists for this user on Solana chain (since this is for creating Solana wallets)
            const existingWalletWithSameNameOnSolana = wallets.find(
                (w) => w.name?.trim().toLowerCase() === input.name.trim().toLowerCase() && w.chain === ChainType.SOLANA,
            );

            if (existingWalletWithSameNameOnSolana) {
                throw new ApiError(WALLET_NAME_ALREADY_EXISTS);
            }

            const response = await this.client.getActivity({
                organizationId: user.subOrgId,
                activityId: input.activityId,
            });

            if (response?.activity?.type !== 'ACTIVITY_TYPE_CREATE_WALLET_ACCOUNTS') {
                throw new ApiError(TURNKEY_INVALID_ACTIVITY);
            }

            if (!response.activity.intent?.createWalletAccountsIntent) {
                throw new ApiError(TURNKEY_INVALID_ACTIVITY);
            }

            if (response.activity.intent.createWalletAccountsIntent?.accounts.length !== 1) {
                throw new ApiError(TURNKEY_INVALID_ACTIVITY);
            }

            if (response.activity.intent.createWalletAccountsIntent?.walletId !== wallets[0].walletId) {
                throw new ApiError(TURNKEY_INVALID_ACTIVITY);
            }

            for (const account of response.activity.intent.createWalletAccountsIntent.accounts) {
                if (account.curve !== 'CURVE_ED25519') {
                    // invalid account create, currently allow solana only
                    throw new ApiError(TURNKEY_INVALID_ACTIVITY);
                }

                if (account.addressFormat !== 'ADDRESS_FORMAT_SOLANA') {
                    // invalid account create, currently allow solana only
                    throw new ApiError(TURNKEY_INVALID_ACTIVITY);
                }
            }

            const approveClient = new Turnkey({
                apiBaseUrl: 'https://api.turnkey.com',
                apiPrivateKey: appConfig.TURNKEY_API_PRIVATE_KEY,
                apiPublicKey: appConfig.TURNKEY_API_PUBLIC_KEY,
                defaultOrganizationId: appConfig.TURNKEY_ORGANIZATION_ID,
            }).apiClient({
                apiPublicKey: appConfig.TURNKEY_API_PUBLIC_KEY,
                apiPrivateKey: appConfig.TURNKEY_API_PRIVATE_KEY,
            });

            const approveActivity = await approveClient.approveActivity({
                organizationId: user.subOrgId,
                fingerprint: response.activity.fingerprint,
            });

            if (approveActivity?.activity?.failure) {
                this.logger.error('Failed to create wallet activity', {
                    activityId: input.activityId,
                    error: approveActivity.activity.failure,
                });

                throw new ApiError(FAILED_TO_CREATE_WALLET);
            }

            const newWallet = await this.client.getWalletAccount({
                organizationId: user.subOrgId,
                walletId: wallets[0].walletId,
                address: approveActivity.createWalletAccountsResult?.addresses[0],
            });

            const embeddedWallets = await this.userService.createUserEmbeddedWallets(user, [
                {
                    walletAddress: newWallet.account.address,
                    chain: ChainType.SOLANA,
                    walletId: newWallet.account.walletId,
                    walletAccountId: newWallet.account.walletAccountId,
                    hdPath: newWallet.account.path,
                    name: input.name,
                },
            ]);

            return {
                wallet: embeddedWallets[0],
            };
        } catch (error) {
            if (error instanceof ApiError) {
                throw error;
            }

            this.logger.error('Failed to approve export account private key', { error });
            throw new ApiError(FAILED_TO_CREATE_WALLET);
        }
    }

    async withdrawTransaction(params: ApproveWithdrawTransactionDto): Promise<string> {
        const user = await this.userService.getUserById(params.userId);
        if (!user || !user.subOrgId) {
            throw new ApiError(USER_NOT_FOUND);
        }

        const wallets = await this.userService.getUserEmbeddedWalletsWithoutBalance(user);

        const wallet = wallets.find((w) => w.chain === ChainType.SOLANA && w.walletAddress === params.address);

        if (!wallet) {
            throw new ApiError(WALLET_NOT_FOUND);
        }

        const verifiedUser = await this.reverifyUserAuthentication(user, {
            message: params.message,
            signature: params.signature,
            isOkxWallet: params.isOkxWallet,
            oidcToken: params.oidcToken,
            otpId: params.otpId,
            otpCode: params.otpCode,
        });

        if (!verifiedUser) {
            throw new ApiError(INVALID_USER_VERIFICATION);
        }

        try {
            const activity = await this.client.getActivity({
                organizationId: user.subOrgId,
                activityId: params.activityId,
            });

            if (!activity || !activity.activity || !activity.activity.intent.signTransactionIntentV2) {
                throw new ApiError(TURNKEY_INVALID_ACTIVITY);
            }

            const transactionBuff = Buffer.from(
                activity.activity.intent.signTransactionIntentV2?.unsignedTransaction,
                'hex',
            );

            const transaction = VersionedTransaction.deserialize(transactionBuff);

            const compiledInstructions = transaction.message.compiledInstructions;
            const accountKeys = transaction.message.staticAccountKeys;

            // Target recipient to verify
            const expectedRecipient = new PublicKey(params.receiveAddress);
            const walletPublicKey = new PublicKey(wallet.walletAddress);

            let isSystemTransfer = false;
            let amountTransferred = 0;

            if (params.token === 'So11111111111111111111111111111111111111112') {
                for (const ix of compiledInstructions) {
                    const programId = accountKeys[ix.programIdIndex];

                    if (programId.equals(SystemProgram.programId)) {
                        const from = accountKeys[ix.accountKeyIndexes[0]];
                        const to = accountKeys[ix.accountKeyIndexes[1]];
                        const data = ix.data; // Buffer
                        const instructionType = Buffer.from(data).readUInt8(0);

                        if (instructionType === 2) {
                            const lamports = Buffer.from(data).readBigUInt64LE(4);

                            if (to.equals(expectedRecipient) && from.equals(walletPublicKey)) {
                                isSystemTransfer = true;
                                amountTransferred = Number(lamports);
                            }
                        }
                    }
                }

                if (
                    !isSystemTransfer ||
                    !new BigNumber(params.amount).multipliedBy(LAMPORTS_PER_SOL).isEqualTo(amountTransferred)
                ) {
                    throw new ApiError(INVALID_WITHDRAW_TRANSACTION);
                }
            } else {
                const tokenProgram = new PublicKey('TokenkegQfeZyiNwAJbNbGKPFXCWuBvf9Ss623VQ5DA');
                const expectedMint = new PublicKey(params.token);
                const tokenRecipient = await getAssociatedTokenAddress(expectedMint, expectedRecipient);

                for (const ix of compiledInstructions) {
                    const programId = accountKeys[ix.programIdIndex];

                    if (!programId.equals(tokenProgram)) continue;

                    const keys = ix.accountKeyIndexes.map((i) => accountKeys[i]);
                    const data = Buffer.from(ix.data);

                    const instructionType = data[0];

                    // SPL Token: TransferChecked (0x12)
                    if (instructionType === 0x12 && data.length >= 10) {
                        // Extract amount (8 bytes LE) and decimals (1 byte)
                        const amount = data.readBigUInt64LE(1);

                        // keys: [source, mint, destination, authority]
                        const destination = keys[2];
                        const mint = keys[1];

                        if (
                            !destination.equals(expectedRecipient) ||
                            !mint.equals(expectedMint) ||
                            !new BigNumber(params.amount).multipliedBy(params.decimals).isEqualTo(amount.toString())
                        ) {
                            throw new ApiError(INVALID_WITHDRAW_TRANSACTION);
                        }
                    }

                    // Also handle Transfer (unverified)
                    if (instructionType === 3 && data.length >= 9) {
                        const amount = data.readBigUInt64LE(1);
                        const destination = keys[1];

                        if (
                            !destination.equals(tokenRecipient) ||
                            !new BigNumber(params.amount)
                                .multipliedBy(10 ** params.decimals)
                                .isEqualTo(amount.toString())
                        ) {
                            throw new ApiError(INVALID_WITHDRAW_TRANSACTION);
                        }
                    }
                }
            }

            const approveClient = new Turnkey({
                apiBaseUrl: 'https://api.turnkey.com',
                apiPrivateKey: appConfig.TURNKEY_API_PRIVATE_KEY,
                apiPublicKey: appConfig.TURNKEY_API_PUBLIC_KEY,
                defaultOrganizationId: appConfig.TURNKEY_ORGANIZATION_ID,
            }).apiClient({
                apiPublicKey: appConfig.TURNKEY_API_PUBLIC_KEY,
                apiPrivateKey: appConfig.TURNKEY_API_PRIVATE_KEY,
            });

            const approveActivity = await approveClient.approveActivity({
                organizationId: user.subOrgId,
                fingerprint: activity.activity.fingerprint,
            });

            if (approveActivity?.activity?.failure) {
                this.logger.error('Failed to approve withdraw wallet activity', {
                    activityId: params.activityId,
                    error: approveActivity.activity.failure,
                });

                throw new ApiError(FAILED_TO_CREATE_WALLET);
            }

            return approveActivity.signTransactionResult?.signedTransaction ?? '';
        } catch (error) {
            if (error instanceof ApiError) {
                throw error;
            }

            this.logger.error('Failed to withdraw transaction', { error });
            throw new ApiError(FAILED_TO_WITHDRAW_TRANSACTION);
        }
    }

    async approveDepositHyperLiquidTransaction(
        userId: string,
        params: ApproveDepositHyperLiquidInput,
    ): Promise<string> {
        const user = await this.userService.getUserById(userId);
        if (!user || !user.subOrgId) {
            throw new ApiError(USER_NOT_FOUND);
        }

        const wallets = await this.userService.getUserEmbeddedWalletsWithoutBalance(user);

        const wallet = wallets.find((w) => w.chain === ChainType.ARB && w.walletAddress === params.wallet);

        if (!wallet) {
            throw new ApiError(WALLET_NOT_FOUND);
        }

        try {
            const activity = await this.client.getActivity({
                organizationId: user.subOrgId,
                activityId: params.activityId,
            });

            if (!activity || !activity.activity || !activity.activity.intent.signTransactionIntentV2) {
                throw new ApiError(TURNKEY_INVALID_ACTIVITY);
            }

            // todo validate
            // const transactionBuff = Buffer.from(
            //     activity.activity.intent.signTransactionIntentV2?.unsignedTransaction,
            //     'hex',
            // );
            // const transaction = VersionedTransaction.deserialize(transactionBuff);

            const approveClient = new Turnkey({
                apiBaseUrl: 'https://api.turnkey.com',
                apiPrivateKey: appConfig.TURNKEY_API_PRIVATE_KEY,
                apiPublicKey: appConfig.TURNKEY_API_PUBLIC_KEY,
                defaultOrganizationId: appConfig.TURNKEY_ORGANIZATION_ID,
            }).apiClient({
                apiPublicKey: appConfig.TURNKEY_API_PUBLIC_KEY,
                apiPrivateKey: appConfig.TURNKEY_API_PRIVATE_KEY,
            });

            const approveActivity = await approveClient.approveActivity({
                organizationId: user.subOrgId,
                fingerprint: activity.activity.fingerprint,
            });

            if (approveActivity?.activity?.failure) {
                this.logger.error('Failed to approve withdraw wallet activity', {
                    activityId: params.activityId,
                    error: approveActivity.activity.failure,
                });

                throw new ApiError(FAILED_TO_CREATE_WALLET);
            }

            return '0x' + (approveActivity.signTransactionResult?.signedTransaction ?? '');
        } catch (error) {
            if (error instanceof ApiError) {
                throw error;
            }

            this.logger.error('Failed to withdraw transaction', { error });
            throw new ApiError(FAILED_TO_WITHDRAW_TRANSACTION);
        }
    }
}

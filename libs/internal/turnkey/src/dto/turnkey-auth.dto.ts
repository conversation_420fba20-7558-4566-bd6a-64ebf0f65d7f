import { Field, Float, InputType, ObjectType, registerEnumType } from '@nestjs/graphql';
import { AuthProvider } from 'libs/internal/users/entities/user.entity';
import { TurnkeyApiTypes } from '@turnkey/http';
import { EmbeddedWalletDto, UserEmbeddedWalletDTO } from 'libs/internal/users/dto/embedded-wallet.dto';
import { UserEmbeddedWallet } from 'libs/internal/users/entities/user-embedded-wallet.entity';
import { IsString, IsNotEmpty, Length, Matches } from 'class-validator';
import { WALLET_NAME_MIN_LENGTH, WALLET_NAME_MAX_LENGTH, WALLET_NAME_REGEX } from 'libs/internal/users/user.constants';
import { ChainType } from '@protogen/user/v1/signing';

export type OauthProviderParams = TurnkeyApiTypes['v1OauthProviderParams'];
export type ApiKeyParams = TurnkeyApiTypes['v1ApiKeyParamsV2'];

export enum TurnkeyLoginType {
    OAUTH = 'oauth',
    OTP = 'otp',
}

export enum OtpType {
    EMAIL = 'OTP_TYPE_EMAIL',
    SMS = 'OTP_TYPE_SMS',
}

registerEnumType(TurnkeyLoginType, {
    name: 'TurnkeyLoginType',
    description: 'Supported login methods for Turnkey',
});

registerEnumType(OtpType, {
    name: 'OtpType',
    description: 'Supported OTP types for Turnkey authentication',
});

@InputType()
export class InputLoginTurnkeyDto {
    @Field(() => TurnkeyLoginType, { nullable: false })
    type: TurnkeyLoginType;

    @Field(() => AuthProvider, { nullable: false })
    provider: AuthProvider;

    @Field(() => String, { nullable: true })
    iodcToken: string;

    @Field(() => String, { nullable: true })
    signature: string;
}

@InputType()
export class InputLoginTelegramV2Dto {
    @Field(() => Float)
    id: number;

    @Field(() => String)
    firstName: string;

    @Field(() => String)
    username: string;

    @Field(() => String)
    photoUrl: string;

    @Field(() => Float)
    authDate: number;

    @Field(() => String)
    hash: string;
}

export class TurnKeyLoginResponseDto {
    organizationId: string;
    organizationName: string;
    userId: string;
    username: string;
    turnkeyErrorCode?: string;
}

@InputType()
export class InitOtpAuthInputDto {
    @Field(() => String, { description: 'Email address or phone number' })
    contact: string;

    @Field(() => String, { nullable: true, description: 'User identifier for rate limiting (recommended: hash of IP)' })
    userIdentifier?: string;

    @Field(() => String, { nullable: true })
    referrerCode?: string;

    @Field(() => String, { nullable: true })
    fingerprint?: string;
}

@InputType()
export class RequestReverifyOtpInputDto {
    @Field(() => String, { nullable: true })
    fingerprint?: string;
}

@ObjectType()
export class InitOtpAuthResponseDto {
    @Field(() => String, { description: 'OTP ID for verification' })
    otpId: string;

    @Field(() => String, { description: 'User ID in the system' })
    userId: string;

    @Field(() => String, { description: 'Sub-organization ID' })
    subOrgId: string;

    @Field(() => Number, { description: 'Lifetime of otp in seconds' })
    ttl: number;
}

@InputType()
export class OtpAuthInputDto {
    @Field(() => String, { description: 'OTP ID from initialization' })
    otpId: string;

    @Field(() => String, { description: 'OTP code received by user' })
    otpCode: string;

    @Field(() => String, { description: 'Target public key for credential encryption' })
    targetPublicKey: string;

    @Field(() => String, { nullable: true, description: 'Name for the generated API key' })
    apiKeyName?: string;

    @Field(() => Float, {
        nullable: true,
        description: 'API key validity in seconds (default: 900)',
        defaultValue: 900,
    })
    expirationSeconds?: number;
}

@ObjectType()
export class OtpAuthResponseDto {
    @Field(() => String, { description: 'Generated API key' })
    apiKey: string;

    @Field(() => String, { description: 'Credential bundle' })
    credentialBundle: string;

    @Field(() => String, { description: 'User ID' })
    userId: string;

    @Field(() => String, { description: 'Sub-organization ID' })
    subOrgId: string;
}

@InputType()
export class ReverifyAuthenticateInput {
    @Field(() => String, { nullable: true, description: 'Signature of signing request if user login by 3rd wallet' })
    signature?: string;

    @Field(() => String, { nullable: true, description: 'Message was signed by user' })
    message?: string;

    @Field(() => Boolean, { nullable: true, description: 'Is user login by OKX wallet' })
    isOkxWallet?: boolean;

    @Field(() => String, { nullable: true, description: 'OIDC token for user who is authenticated by Google' })
    oidcToken?: string;

    @Field(() => String, { nullable: true, description: 'OTP ID for user who is authenticated by email OTP' })
    otpId?: string;

    @Field(() => String, { nullable: true, description: 'OTP code for user who is authenticated by email OTP' })
    otpCode?: string;
}

@InputType()
export class ExportPassphraseInput extends ReverifyAuthenticateInput {
    @Field(() => String)
    publicKey: string;

    @Field(() => String)
    activityId: string;
}

@ObjectType()
export class ApprovedPassphraseResponse {
    @Field(() => String)
    activityId: string;

    @Field(() => String)
    passphrase: string;
}

@ObjectType()
export class ApprovedPrivateKeyResponse {
    @Field(() => String)
    activityId: string;

    @Field(() => String)
    privateKey: string;
}

@ObjectType()
export class ApprovedCreateWalletResponse {
    @Field(() => UserEmbeddedWallet)
    wallet: UserEmbeddedWallet;
}

@ObjectType()
export class MarkedAsExportedPassphraseResponse {
    @Field(() => Boolean)
    updated: boolean;
}

export class ReverifyUserAuthenticationDto {
    oidcToken?: string;
    otpId?: string;
    otpCode?: string;
    signature?: string;
    message?: string;
    isOkxWallet?: boolean;
}

@InputType()
export class ExportPrivateKeyInput extends ReverifyAuthenticateInput {
    @Field(() => String)
    publicKey: string;

    @Field(() => String)
    activityId: string;
}

@InputType()
export class ExportPrivateKeyWithoutVerifyInput {
    @Field(() => String)
    publicKey: string;

    @Field(() => String)
    activityId: string;
}

@InputType()
export class ApproveCreateWalletInput {
    @Field(() => String)
    activityId: string;

    @Field(() => String, { description: `New wallet name, max length is ${WALLET_NAME_MAX_LENGTH}` })
    @IsString({ message: 'Wallet name must be a string' })
    @IsNotEmpty({ message: 'Wallet name cannot be empty' })
    @Length(WALLET_NAME_MIN_LENGTH, WALLET_NAME_MAX_LENGTH, {
        message: `Wallet name must be between ${WALLET_NAME_MIN_LENGTH} and ${WALLET_NAME_MAX_LENGTH} characters`,
    })
    @Matches(WALLET_NAME_REGEX, {
        message: 'Wallet name can only contain letters, numbers, spaces, hyphens, underscores, and dots',
    })
    name: string;
}

@InputType()
export class ApproveDepositHyperLiquidInput {
    @Field(() => String, { description: 'Activity ID for the withdrawal request in Turnkey' })
    activityId: string;

    @Field(() => Float, { description: 'Number of USDC amount' })
    amount: string;

    @Field(() => String, { description: 'Wallet address of account that is used to withdraw' })
    wallet: string;
}

@ObjectType()
export class ApproveDepositHyperLiquidResponse {
    @Field(() => String, { description: 'Signed transaction string' })
    signedTransaction: string;
}

export class ApproveWithdrawTransactionDto {
    userId: string;
    chain: ChainType;
    address: string;
    receiveAddress: string;
    amount: string;
    activityId: string;
    signature?: string;
    message?: string;
    isOkxWallet?: boolean;
    oidcToken?: string;
    otpId?: string;
    otpCode?: string;
    token: string;
    decimals: number;
}

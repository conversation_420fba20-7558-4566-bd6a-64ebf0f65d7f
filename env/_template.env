STAGE={{ .STAGE }}
PORT=3000
INTERNAL_GRPC_PORT=5001
JWT_SECRET={{ .JWT_SECRET }}
JWT_REFRESH_SECRET={{ .JWT_REFRESH_SECRET }}
ENCRYPT_PUBLIC_KEY={{ .ENCRYPT_PUBLIC_KEY }}

# PG config
# ------------------------------------------------------------------
POSTGRES_HOST={{ .POSTGRES_HOST }}
POSTGRES_PORT={{ .POSTGRES_PORT }}
POSTGRES_USER={{ .POSTGRES_USER }}
POSTGRES_PASS={{ .POSTGRES_PASS }}
POSTGRES_DB_XBIT={{ .POSTGRES_DB_XBIT }}
POSTGRES_SSL_MODE={{ .POSTGRES_SSL_MODE }}

# Redis config
# ------------------------------------------------------------------
REDIS_HOST={{ .REDIS_HOST }}
REDIS_PORT={{ .REDIS_PORT }}
REDIS_PASS={{ .REDIS_PASS }}
REDIS_DB={{ .REDIS_DB }}

# Telegram bot config
# ------------------------------------------------------------------
TELEGRAM_BOT_DOMAIN={{ .TELEGRAM_BOT_DOMAIN }}
TELEGRAM_BOT_WEBHOOK={{ .TELEGRAM_BOT_WEBHOOK }}
TELEGRAM_BOT_AUTH_TOKEN={{ .TELEGRAM_BOT_AUTH_TOKEN }}
TELEGRAM_LOGIN_URL={{ .TELEGRAM_LOGIN_URL  }}

# Google OAuth Configuration
# ------------------------------------------------------------------
GOOGLE_CLIENT_ID={{ .GOOGLE_CLIENT_ID }}
GOOGLE_CLIENT_SECRET={{ .GOOGLE_CLIENT_SECRET }}
GOOGLE_OIDC={{ .GOOGLE_OIDC }}

#Telegram tranding bot config
#-------------------------------------------------------------------
TELEGRAM_BOT_SNIPER_TOKEN={{ .TELEGRAM_BOT_SNIPER_TOKEN }}

# Wallet service config
# ------------------------------------------------------------------
WALLET_SERVICE_HOST={{ .WALLET_SERVICE_HOST }}
WALLET_SERVICE_PORT={{ .WALLET_SERVICE_PORT }}
WALLET_SERVICE_APIKEY={{ .WALLET_SERVICE_APIKEY}}

#ETH
# ------------------------------------------------------------------
INFURA_RPC_URL={{  .INFURA_RPC_URL  }}
TRON_GRID_API_KEY={{  .TRON_GRID_API_KEY  }}

#EMQX
EMQX_PROTOCOL={{  .EMQX_PROTOCOL  }}
EMQX_HOST={{  .EMQX_HOST  }}
EMQX_PORT={{  .EMQX_PORT  }}
EMQX_USER={{  .EMQX_USER  }}
EMQX_PASS={{  .EMQX_PASS  }}

#XBIT LANDING PAGE
LANDING_PAGE_URL={{  .LANDING_PAGE_URL  }}

MEME_BASE_URL={{ .MEME_BASE_URL }}
TRANDING_BASE_URL={{ .TRANDING_BASE_URL }}

REFERRAL_ADDRESS={{ .REFERRAL_ADDRESS }}
WEBSTITE_ADDRESS={{ .WEBSTITE_ADDRESS }}

TOKEN_DETAILS={{ .TOKEN_DETAILS }}

ACCESS_TOKEN_EXPIRES_IN=1d
REFRESH_TOKEN_EXPIRES_IN=30d

NATS_URL={{ .NATS_URL }}
NATS_AUTH_TOKEN={{ .NATS_AUTH_TOKEN }}
NATS_USER={{ .NATS_USER }}
NATS_PASS={{ .NATS_PASS }}

JWT_OIDC={{ .JWT_OIDC }}
TURNKEY_API_BASE_URL=https://api.turnkey.com
TURNKEY_API_PRIVATE_KEY={{ .TURNKEY_API_PRIVATE_KEY }}
TURNKEY_API_PUBLIC_KEY={{ .TURNKEY_API_PUBLIC_KEY }}
TURNKEY_ORGANIZATION_ID={{ .TURNKEY_ORGANIZATION_ID }}

REDIS_CLUSTER_HOST={{  .REDIS_CLUSTER_HOST  }}
REDIS_CLUSTER_PORT={{  .REDIS_CLUSTER_PORT  }}
REDIS_CLUSTER_ENABLE_SSL={{  .REDIS_CLUSTER_ENABLE_SSL  }}
REDIS_CLUSTER_PRIMARY_PASS={{  .REDIS_CLUSTER_PRIMARY_PASS  }}
REDIS_CLUSTER_SECONDARY_PASS={{  .REDIS_CLUSTER_SECONDARY_PASS  }}

TURNKEY_FAKE_PUBLIC_KEY=033ff0089011ed1b7155d015541e4987fb8382fc560f7ec8f3c267e0886c974f7a
TURNKEY_FAKE_PRIVATE_KEY=a9a0dfb3856a1ec2563c05bbdb788a6061fe80c937cd8d27c93842ea1e5c4951